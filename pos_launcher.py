#!/usr/bin/env python3
"""
Django POS System - Simple Executable Launcher
This file can be converted to .exe using PyInstaller
"""

import os
import sys
import subprocess
import tkinter as tk
from tkinter import messagebox, ttk
import threading
import webbrowser
from pathlib import Path
import time

class POSLauncher:
    def __init__(self):
        self.project_dir = Path(__file__).resolve().parent
        self.server_process = None
        self.server_running = False
        
        # Create GUI
        self.root = tk.Tk()
        self.root.title("Django POS System")
        self.root.geometry("400x300")
        self.root.resizable(False, False)
        
        # Center the window
        self.center_window()
        
        # Create GUI elements
        self.create_widgets()
        
    def center_window(self):
        """Center the window on screen"""
        self.root.update_idletasks()
        x = (self.root.winfo_screenwidth() // 2) - (400 // 2)
        y = (self.root.winfo_screenheight() // 2) - (300 // 2)
        self.root.geometry(f"400x300+{x}+{y}")
    
    def create_widgets(self):
        """Create GUI widgets"""
        # Title
        title_label = tk.Label(
            self.root, 
            text="Django POS System", 
            font=("Arial", 16, "bold"),
            fg="blue"
        )
        title_label.pack(pady=20)
        
        # Status
        self.status_label = tk.Label(
            self.root, 
            text="Ready to start", 
            font=("Arial", 10),
            fg="green"
        )
        self.status_label.pack(pady=10)
        
        # Progress bar
        self.progress = ttk.Progressbar(
            self.root, 
            mode='indeterminate',
            length=300
        )
        self.progress.pack(pady=10)
        
        # Buttons frame
        button_frame = tk.Frame(self.root)
        button_frame.pack(pady=20)
        
        # Start button
        self.start_button = tk.Button(
            button_frame,
            text="🚀 Start POS System",
            font=("Arial", 12, "bold"),
            bg="green",
            fg="white",
            width=15,
            height=2,
            command=self.start_pos_system
        )
        self.start_button.pack(side=tk.LEFT, padx=10)
        
        # Stop button
        self.stop_button = tk.Button(
            button_frame,
            text="⏹️ Stop System",
            font=("Arial", 12, "bold"),
            bg="red",
            fg="white",
            width=15,
            height=2,
            command=self.stop_pos_system,
            state=tk.DISABLED
        )
        self.stop_button.pack(side=tk.LEFT, padx=10)
        
        # Open browser button
        self.browser_button = tk.Button(
            self.root,
            text="🌐 Open in Browser",
            font=("Arial", 10),
            bg="blue",
            fg="white",
            width=20,
            command=self.open_browser,
            state=tk.DISABLED
        )
        self.browser_button.pack(pady=10)
        
        # Exit button
        exit_button = tk.Button(
            self.root,
            text="❌ Exit",
            font=("Arial", 10),
            command=self.exit_application
        )
        exit_button.pack(pady=10)
        
        # URL label
        self.url_label = tk.Label(
            self.root,
            text="",
            font=("Arial", 8),
            fg="blue",
            cursor="hand2"
        )
        self.url_label.pack(pady=5)
        self.url_label.bind("<Button-1>", lambda e: self.open_browser())
    
    def update_status(self, message, color="black"):
        """Update status message"""
        self.status_label.config(text=message, fg=color)
        self.root.update()
    
    def start_pos_system(self):
        """Start the POS system in a separate thread"""
        def start_thread():
            try:
                self.start_button.config(state=tk.DISABLED)
                self.progress.start()
                
                self.update_status("Checking dependencies...", "blue")
                
                # Check if Python and dependencies are available
                if not self.check_dependencies():
                    return
                
                self.update_status("Starting Django server...", "blue")
                
                # Start the server
                if self.start_server():
                    self.server_running = True
                    self.start_button.config(state=tk.DISABLED)
                    self.stop_button.config(state=tk.NORMAL)
                    self.browser_button.config(state=tk.NORMAL)
                    self.url_label.config(text="http://127.0.0.1:8000")
                    self.update_status("✅ POS System is running!", "green")
                    
                    # Auto-open browser
                    time.sleep(2)
                    self.open_browser()
                else:
                    self.update_status("❌ Failed to start server", "red")
                    self.start_button.config(state=tk.NORMAL)
                    
            except Exception as e:
                self.update_status(f"❌ Error: {str(e)}", "red")
                self.start_button.config(state=tk.NORMAL)
            finally:
                self.progress.stop()
        
        threading.Thread(target=start_thread, daemon=True).start()
    
    def check_dependencies(self):
        """Check if dependencies are installed"""
        try:
            # Check Python
            result = subprocess.run([sys.executable, "--version"], 
                                  capture_output=True, text=True)
            if result.returncode != 0:
                messagebox.showerror("Error", "Python not found!")
                return False
            
            # Check Django
            result = subprocess.run([sys.executable, "-c", "import django"], 
                                  capture_output=True, text=True)
            if result.returncode != 0:
                messagebox.showerror("Error", "Django not installed!\nPlease run: pip install -r requirements_desktop.txt")
                return False
            
            return True
            
        except Exception as e:
            messagebox.showerror("Error", f"Dependency check failed: {str(e)}")
            return False
    
    def start_server(self):
        """Start the Django server"""
        try:
            # Run migrations first
            self.update_status("Running migrations...", "blue")
            subprocess.run([
                sys.executable, "manage.py", "migrate"
            ], cwd=self.project_dir, check=True, capture_output=True)
            
            # Collect static files
            self.update_status("Collecting static files...", "blue")
            subprocess.run([
                sys.executable, "manage.py", "collectstatic", "--noinput"
            ], cwd=self.project_dir, capture_output=True)
            
            # Start server
            self.update_status("Starting server...", "blue")
            self.server_process = subprocess.Popen([
                sys.executable, "manage.py", "runserver", "127.0.0.1:8000", "--noreload"
            ], cwd=self.project_dir, stdout=subprocess.PIPE, stderr=subprocess.PIPE)
            
            # Wait for server to start
            for i in range(15):
                try:
                    import urllib.request
                    urllib.request.urlopen("http://127.0.0.1:8000", timeout=1)
                    return True
                except:
                    time.sleep(1)
                    self.update_status(f"Starting server... ({i+1}/15)", "blue")
            
            return False
            
        except Exception as e:
            messagebox.showerror("Error", f"Failed to start server: {str(e)}")
            return False
    
    def stop_pos_system(self):
        """Stop the POS system"""
        try:
            if self.server_process:
                self.server_process.terminate()
                self.server_process.wait(timeout=5)
                self.server_process = None
            
            self.server_running = False
            self.start_button.config(state=tk.NORMAL)
            self.stop_button.config(state=tk.DISABLED)
            self.browser_button.config(state=tk.DISABLED)
            self.url_label.config(text="")
            self.update_status("⏹️ POS System stopped", "orange")
            
        except Exception as e:
            messagebox.showerror("Error", f"Failed to stop server: {str(e)}")
    
    def open_browser(self):
        """Open POS system in browser"""
        if self.server_running:
            webbrowser.open("http://127.0.0.1:8000")
        else:
            messagebox.showwarning("Warning", "POS System is not running!")
    
    def exit_application(self):
        """Exit the application"""
        if self.server_running:
            if messagebox.askyesno("Confirm Exit", "POS System is running. Stop and exit?"):
                self.stop_pos_system()
                self.root.quit()
        else:
            self.root.quit()
    
    def run(self):
        """Run the GUI application"""
        # Handle window close
        self.root.protocol("WM_DELETE_WINDOW", self.exit_application)
        
        # Start the GUI
        self.root.mainloop()

if __name__ == "__main__":
    # Set environment variable for Django settings
    os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'desktop_app_settings')
    
    # Create and run the launcher
    launcher = POSLauncher()
    launcher.run()
