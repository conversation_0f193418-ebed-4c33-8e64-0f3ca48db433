"""
Django management command to populate the POS system with common categories and products.
Usage: python manage.py populate_data
"""

from django.core.management.base import BaseCommand, CommandError
from django.db import transaction
from pos.models import Category, Product
from decimal import Decimal
import random
from datetime import datetime, timedelta


class Command(BaseCommand):
    help = 'Populate the database with common POS categories and products'

    def add_arguments(self, parser):
        parser.add_argument(
            '--categories-only',
            action='store_true',
            help='Only create categories, skip products',
        )
        parser.add_argument(
            '--products-only',
            action='store_true',
            help='Only create products (requires existing categories)',
        )
        parser.add_argument(
            '--clear-existing',
            action='store_true',
            help='Clear existing data before populating',
        )

    def handle(self, *args, **options):
        self.stdout.write(
            self.style.SUCCESS('🚀 Starting POS data population...\n')
        )

        # Clear existing data if requested
        if options['clear_existing']:
            self.clear_existing_data()

        try:
            with transaction.atomic():
                if not options['products_only']:
                    self.create_categories()
                
                if not options['categories_only']:
                    self.create_products()
                
                self.stdout.write(
                    self.style.SUCCESS('\n✅ Data population completed successfully!')
                )
                self.display_summary()
                
        except Exception as e:
            self.stdout.write(
                self.style.ERROR(f'❌ Error during population: {str(e)}')
            )
            raise CommandError(f'Population failed: {str(e)}')

    def clear_existing_data(self):
        """Clear existing categories and products"""
        self.stdout.write('🧹 Clearing existing data...')
        
        product_count = Product.objects.count()
        category_count = Category.objects.count()
        
        Product.objects.all().delete()
        Category.objects.all().delete()
        
        self.stdout.write(
            self.style.WARNING(
                f'   Deleted {product_count} products and {category_count} categories'
            )
        )

    def create_categories(self):
        """Create common POS categories"""
        self.stdout.write('📁 Creating categories...')
        
        categories_data = [
            # Food & Beverages
            'Beverages',
            'Snacks & Chips',
            'Candy & Sweets',
            'Dairy Products',
            'Frozen Foods',
            'Canned Goods',
            'Bakery Items',
            'Fresh Fruits',
            'Vegetables',
            'Meat & Poultry',
            
            # Personal Care
            'Personal Hygiene',
            'Health & Medicine',
            'Beauty Products',
            'Baby Care',
            
            # Household
            'Cleaning Supplies',
            'Paper Products',
            'Kitchen Supplies',
            'Laundry Care',
            
            # Electronics & Accessories
            'Electronics',
            'Phone Accessories',
            'Batteries',
            
            # Stationery & Office
            'Stationery',
            'Office Supplies',
            'Books & Magazines',
            
            # Clothing & Accessories
            'Clothing',
            'Accessories',
            'Footwear',
            
            # Automotive
            'Car Accessories',
            'Motor Oil',
            
            # Miscellaneous
            'Toys & Games',
            'Pet Supplies',
            'Hardware',
            'Seasonal Items',
        ]

        created_categories = []
        for category_name in categories_data:
            category, created = Category.objects.get_or_create(
                name=category_name
            )
            if created:
                created_categories.append(category)
                self.stdout.write(f'   ✓ Created: {category_name}')
            else:
                self.stdout.write(f'   ⚠ Already exists: {category_name}')

        self.stdout.write(
            self.style.SUCCESS(f'📁 Created {len(created_categories)} new categories')
        )

    def create_products(self):
        """Create common products for each category"""
        self.stdout.write('🛍️ Creating products...')
        
        categories = Category.objects.all()
        if not categories.exists():
            raise CommandError('No categories found. Create categories first or run without --products-only')

        products_data = self.get_products_data()
        created_products = []

        for category in categories:
            category_products = products_data.get(category.name, [])
            
            if category_products:
                self.stdout.write(f'   📦 Adding products to {category.name}...')
                
                for product_info in category_products:
                    product, created = Product.objects.get_or_create(
                        name=product_info['name'],
                        category=category,
                        defaults={
                            'barcode': self.generate_barcode(),
                            'price': Decimal(str(product_info['price'])),
                            'stock_quantity': random.randint(10, 100),
                            'description': product_info.get('description', f'{product_info["name"]} - {category.name}'),
                            'expiration_date': self.generate_expiration_date(category.name) if self.needs_expiration(category.name) else None,
                        }
                    )
                    
                    if created:
                        created_products.append(product)
                        self.stdout.write(f'      ✓ {product_info["name"]} - ₱{product_info["price"]}')
                    else:
                        self.stdout.write(f'      ⚠ Already exists: {product_info["name"]}')

        self.stdout.write(
            self.style.SUCCESS(f'🛍️ Created {len(created_products)} new products')
        )

    def get_products_data(self):
        """Return dictionary of products organized by category"""
        return {
            'Beverages': [
                {'name': 'Coca-Cola 330ml', 'price': 25.00},
                {'name': 'Pepsi 330ml', 'price': 25.00},
                {'name': 'Sprite 330ml', 'price': 25.00},
                {'name': 'Mountain Dew 330ml', 'price': 25.00},
                {'name': 'Royal Tru Orange 330ml', 'price': 20.00},
                {'name': 'Bottled Water 500ml', 'price': 15.00},
                {'name': 'Gatorade 500ml', 'price': 35.00},
                {'name': 'Red Bull 250ml', 'price': 65.00},
                {'name': 'Coffee Black 3-in-1', 'price': 8.00},
                {'name': 'Milo Drink 240ml', 'price': 30.00},
            ],
            'Snacks & Chips': [
                {'name': 'Lay\'s Classic 60g', 'price': 45.00},
                {'name': 'Pringles Original 110g', 'price': 85.00},
                {'name': 'Cheetos Cheese 65g', 'price': 40.00},
                {'name': 'Doritos Nacho 48g', 'price': 35.00},
                {'name': 'Nova Multigrain 78g', 'price': 25.00},
                {'name': 'Chippy Barbecue 110g', 'price': 30.00},
                {'name': 'Oishi Prawn Crackers 60g', 'price': 20.00},
                {'name': 'Ricoa Curls 65g', 'price': 25.00},
            ],
            'Candy & Sweets': [
                {'name': 'Mentos Mint Roll', 'price': 15.00},
                {'name': 'Tic Tac Orange', 'price': 25.00},
                {'name': 'Ricola Honey Lemon', 'price': 35.00},
                {'name': 'Halls Cough Drops', 'price': 30.00},
                {'name': 'Chupa Chups Lollipop', 'price': 10.00},
                {'name': 'Hershey\'s Kisses 43g', 'price': 65.00},
                {'name': 'Snickers Bar 50g', 'price': 45.00},
                {'name': 'Kit Kat 4-finger 41g', 'price': 40.00},
            ],
            'Dairy Products': [
                {'name': 'Fresh Milk 1L', 'price': 85.00},
                {'name': 'Cheese Singles 8pcs', 'price': 120.00},
                {'name': 'Butter 200g', 'price': 95.00},
                {'name': 'Yogurt Cup 125g', 'price': 35.00},
                {'name': 'Cream Cheese 200g', 'price': 150.00},
                {'name': 'Condensed Milk 300ml', 'price': 45.00},
                {'name': 'Evaporated Milk 354ml', 'price': 35.00},
            ],
            'Personal Hygiene': [
                {'name': 'Toothpaste 100g', 'price': 65.00},
                {'name': 'Toothbrush Soft', 'price': 35.00},
                {'name': 'Shampoo Sachet 12ml', 'price': 8.00},
                {'name': 'Soap Bar 90g', 'price': 25.00},
                {'name': 'Deodorant Spray 150ml', 'price': 85.00},
                {'name': 'Tissue Pack 10pcs', 'price': 15.00},
                {'name': 'Wet Wipes 10pcs', 'price': 25.00},
            ],
            'Cleaning Supplies': [
                {'name': 'Dishwashing Liquid 250ml', 'price': 35.00},
                {'name': 'Laundry Detergent 1kg', 'price': 125.00},
                {'name': 'Floor Cleaner 500ml', 'price': 45.00},
                {'name': 'Toilet Paper 4 rolls', 'price': 85.00},
                {'name': 'Sponge 3pcs', 'price': 25.00},
                {'name': 'Garbage Bags 20pcs', 'price': 35.00},
            ],
            'Electronics': [
                {'name': 'USB Cable Type-C 1m', 'price': 150.00},
                {'name': 'Phone Charger 5V 2A', 'price': 250.00},
                {'name': 'Earphones 3.5mm', 'price': 185.00},
                {'name': 'Power Bank 10000mAh', 'price': 850.00},
                {'name': 'Memory Card 32GB', 'price': 450.00},
                {'name': 'Phone Case Clear', 'price': 125.00},
            ],
            'Stationery': [
                {'name': 'Ballpen Blue', 'price': 8.00},
                {'name': 'Pencil HB', 'price': 5.00},
                {'name': 'Notebook 80 leaves', 'price': 35.00},
                {'name': 'Eraser White', 'price': 5.00},
                {'name': 'Ruler 30cm', 'price': 15.00},
                {'name': 'Stapler Small', 'price': 85.00},
                {'name': 'Correction Tape', 'price': 25.00},
            ],
            'Canned Goods': [
                {'name': 'Corned Beef 150g', 'price': 45.00},
                {'name': 'Sardines in Tomato 155g', 'price': 25.00},
                {'name': 'Tuna Flakes 180g', 'price': 35.00},
                {'name': 'Luncheon Meat 340g', 'price': 85.00},
                {'name': 'Tomato Sauce 250g', 'price': 20.00},
                {'name': 'Coconut Milk 400ml', 'price': 30.00},
            ],
        }

    def generate_barcode(self):
        """Generate a random 13-digit barcode"""
        return ''.join([str(random.randint(0, 9)) for _ in range(13)])

    def generate_expiration_date(self, category_name):
        """Generate appropriate expiration dates based on category"""
        today = datetime.now().date()
        
        # Different shelf lives for different categories
        if 'Dairy' in category_name or 'Fresh' in category_name:
            days = random.randint(3, 14)  # 3-14 days
        elif 'Beverages' in category_name or 'Snacks' in category_name:
            days = random.randint(30, 365)  # 1 month to 1 year
        elif 'Canned' in category_name:
            days = random.randint(365, 1095)  # 1-3 years
        else:
            days = random.randint(90, 730)  # 3 months to 2 years
            
        return today + timedelta(days=days)

    def needs_expiration(self, category_name):
        """Determine if products in this category need expiration dates"""
        expiring_categories = [
            'Dairy Products', 'Fresh Fruits', 'Vegetables', 'Meat & Poultry',
            'Bakery Items', 'Frozen Foods', 'Beverages', 'Snacks & Chips',
            'Candy & Sweets', 'Canned Goods'
        ]
        return category_name in expiring_categories

    def display_summary(self):
        """Display summary of created data"""
        self.stdout.write('\n📊 Summary:')
        self.stdout.write(f'   Categories: {Category.objects.count()}')
        self.stdout.write(f'   Products: {Product.objects.count()}')
        
        # Display categories with product counts
        self.stdout.write('\n📁 Categories with product counts:')
        for category in Category.objects.all():
            product_count = category.products.count()
            self.stdout.write(f'   • {category.name}: {product_count} products')

        self.stdout.write('\n💡 Next steps:')
        self.stdout.write('   • Run: python manage.py populate_sales (to create sample sales)')
        self.stdout.write('   • Visit: http://localhost:8000/products/ (to view products)')
        self.stdout.write('   • Visit: http://localhost:8000/categories/ (to view categories)')
