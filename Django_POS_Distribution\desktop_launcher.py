#!/usr/bin/env python3
"""
Django POS Desktop Application Launcher
Automatically starts the Django server and opens the POS system in the default browser.
"""

import os
import sys
import time
import socket
import subprocess
import webbrowser
import threading
import signal
from pathlib import Path
import logging
from datetime import datetime

# Add the project directory to Python path
PROJECT_DIR = Path(__file__).resolve().parent
sys.path.insert(0, str(PROJECT_DIR))

# Configure logging
LOG_DIR = PROJECT_DIR / 'logs'
LOG_DIR.mkdir(exist_ok=True)
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler(LOG_DIR / f'pos_desktop_{datetime.now().strftime("%Y%m%d")}.log'),
        logging.StreamHandler()
    ]
)
logger = logging.getLogger(__name__)

class DjangoPOSDesktop:
    def __init__(self):
        self.project_dir = PROJECT_DIR
        self.manage_py = self.project_dir / 'manage.py'
        self.server_process = None
        self.port = self.find_available_port()
        self.host = '127.0.0.1'
        self.url = f'http://{self.host}:{self.port}'
        self.server_started = False
        
    def find_available_port(self, start_port=8000, max_attempts=100):
        """Find an available port starting from start_port"""
        for port in range(start_port, start_port + max_attempts):
            try:
                with socket.socket(socket.AF_INET, socket.SOCK_STREAM) as s:
                    s.bind(('127.0.0.1', port))
                    return port
            except OSError:
                continue
        raise RuntimeError(f"No available port found in range {start_port}-{start_port + max_attempts}")
    
    def check_dependencies(self):
        """Check if Django and other dependencies are available"""
        try:
            import django
            logger.info(f"Django version: {django.get_version()}")
            return True
        except ImportError:
            logger.error("Django not found. Please install dependencies.")
            return False
    
    def setup_django_environment(self):
        """Set up Django environment variables"""
        # Use desktop-specific settings
        os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'desktop_app_settings')

        # Import Django and configure
        try:
            import django
            django.setup()
            logger.info("Django environment configured successfully (desktop mode)")
            return True
        except Exception as e:
            logger.error(f"Failed to configure Django: {e}")
            return False
    
    def run_migrations(self):
        """Run Django migrations if needed"""
        try:
            logger.info("Checking for pending migrations...")
            result = subprocess.run([
                sys.executable, str(self.manage_py), 'migrate', '--check'
            ], capture_output=True, text=True, cwd=self.project_dir)
            
            if result.returncode != 0:
                logger.info("Running migrations...")
                subprocess.run([
                    sys.executable, str(self.manage_py), 'migrate'
                ], cwd=self.project_dir, check=True)
                logger.info("Migrations completed successfully")
            else:
                logger.info("No pending migrations")
            return True
        except subprocess.CalledProcessError as e:
            logger.error(f"Migration failed: {e}")
            return False
    
    def collect_static_files(self):
        """Collect static files for production"""
        try:
            logger.info("Collecting static files...")
            subprocess.run([
                sys.executable, str(self.manage_py), 'collectstatic', '--noinput'
            ], cwd=self.project_dir, check=True, capture_output=True)
            logger.info("Static files collected successfully")
            return True
        except subprocess.CalledProcessError as e:
            logger.warning(f"Static files collection failed: {e}")
            return False
    
    def start_django_server(self):
        """Start the Django development server"""
        try:
            logger.info(f"Starting Django server on {self.host}:{self.port}")
            
            # Start server in a separate process
            self.server_process = subprocess.Popen([
                sys.executable, str(self.manage_py), 'runserver', 
                f'{self.host}:{self.port}', '--noreload'
            ], cwd=self.project_dir, stdout=subprocess.PIPE, stderr=subprocess.PIPE)
            
            # Wait for server to start
            max_wait = 30  # seconds
            for i in range(max_wait):
                try:
                    import urllib.request
                    urllib.request.urlopen(self.url, timeout=2)
                    self.server_started = True
                    logger.info(f"Django server started successfully at {self.url}")
                    return True
                except:
                    pass

                time.sleep(1)
                logger.info(f"Waiting for server to start... ({i+1}/{max_wait})")
            
            logger.error("Server failed to start within timeout period")
            return False
            
        except Exception as e:
            logger.error(f"Failed to start Django server: {e}")
            return False
    
    def open_browser(self):
        """Open the POS system in the default web browser"""
        try:
            logger.info(f"Opening browser at {self.url}")
            webbrowser.open(self.url)
            return True
        except Exception as e:
            logger.error(f"Failed to open browser: {e}")
            return False
    
    def cleanup(self):
        """Clean up resources and stop the server"""
        if self.server_process:
            logger.info("Stopping Django server...")
            self.server_process.terminate()
            try:
                self.server_process.wait(timeout=5)
            except subprocess.TimeoutExpired:
                self.server_process.kill()
            logger.info("Django server stopped")
    
    def run(self):
        """Main application entry point"""
        logger.info("=" * 50)
        logger.info("Starting Django POS Desktop Application")
        logger.info("=" * 50)
        
        try:
            # Check dependencies
            if not self.check_dependencies():
                input("Press Enter to exit...")
                return False
            
            # Setup Django environment
            if not self.setup_django_environment():
                input("Press Enter to exit...")
                return False
            
            # Run migrations
            if not self.run_migrations():
                input("Press Enter to exit...")
                return False
            
            # Collect static files
            self.collect_static_files()
            
            # Start Django server
            if not self.start_django_server():
                input("Press Enter to exit...")
                return False
            
            # Open browser
            self.open_browser()
            
            logger.info("=" * 50)
            logger.info("POS System is now running!")
            logger.info(f"Access URL: {self.url}")
            logger.info("Press Ctrl+C to stop the application")
            logger.info("=" * 50)
            
            # Keep the application running
            try:
                while True:
                    time.sleep(1)
            except KeyboardInterrupt:
                logger.info("Shutdown requested by user")
            
            return True
            
        except Exception as e:
            logger.error(f"Application error: {e}")
            return False
        finally:
            self.cleanup()

def signal_handler(signum, frame):
    """Handle shutdown signals"""
    logger.info(f"Received shutdown signal {signum}")
    sys.exit(0)

if __name__ == '__main__':
    # Register signal handlers
    signal.signal(signal.SIGINT, signal_handler)
    signal.signal(signal.SIGTERM, signal_handler)
    
    # Create and run the desktop application
    app = DjangoPOSDesktop()
    success = app.run()
    
    if not success:
        input("Application failed to start. Press Enter to exit...")
        sys.exit(1)
