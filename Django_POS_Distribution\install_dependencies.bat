@echo off
echo Django POS System - Dependency Installer
echo ========================================
echo.

echo Checking Python installation...
python --version >nul 2>&1
if errorlevel 1 (
    echo ERROR: Python is not installed or not in PATH
    echo.
    echo Please install Python from: https://python.org
    echo Make sure to check "Add Python to PATH" during installation
    echo.
    pause
    exit /b 1
)

echo Python found
echo.

echo Installing dependencies...
echo This may take a few minutes...
echo.

REM Try uv first, then fall back to pip
uv pip install -r requirements_desktop.txt >nul 2>&1
if errorlevel 1 (
    echo Using pip to install dependencies...
    python -m pip install -r requirements_desktop.txt
    if errorlevel 1 (
        echo Failed to install dependencies
        echo.
        echo Please try manually:
        echo pip install -r requirements_desktop.txt
        echo.
        pause
        exit /b 1
    )
)

echo.
echo Dependencies installed successfully!
echo.
echo You can now run the POS system using:
echo 1. Django_POS_System.bat (recommended)
echo 2. python pos_launcher.py (GUI version)
echo 3. python desktop_launcher.py (full desktop version)
echo.
pause