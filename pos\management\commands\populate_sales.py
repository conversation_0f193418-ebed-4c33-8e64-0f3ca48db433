"""
Django management command to populate the POS system with sample sales data.
Usage: python manage.py populate_sales
"""

from django.core.management.base import BaseCommand, CommandError
from django.db import transaction
from django.contrib.auth.models import User
from pos.models import Product, Sale, SaleItem
from decimal import Decimal
import random
from datetime import datetime, timedelta


class Command(BaseCommand):
    help = 'Populate the database with sample sales data'

    def add_arguments(self, parser):
        parser.add_argument(
            '--count',
            type=int,
            default=50,
            help='Number of sales to create (default: 50)',
        )
        parser.add_argument(
            '--days',
            type=int,
            default=30,
            help='Number of days back to create sales (default: 30)',
        )
        parser.add_argument(
            '--clear-existing',
            action='store_true',
            help='Clear existing sales before populating',
        )

    def handle(self, *args, **options):
        self.stdout.write(
            self.style.SUCCESS('🛒 Starting sales data population...\n')
        )

        # Check if products exist
        if not Product.objects.exists():
            raise CommandError(
                'No products found. Please run "python manage.py populate_data" first.'
            )

        # Clear existing sales if requested
        if options['clear_existing']:
            self.clear_existing_sales()

        try:
            with transaction.atomic():
                self.create_sales(options['count'], options['days'])
                
                self.stdout.write(
                    self.style.SUCCESS('\n✅ Sales data population completed successfully!')
                )
                self.display_summary()
                
        except Exception as e:
            self.stdout.write(
                self.style.ERROR(f'❌ Error during population: {str(e)}')
            )
            raise CommandError(f'Population failed: {str(e)}')

    def clear_existing_sales(self):
        """Clear existing sales data"""
        self.stdout.write('🧹 Clearing existing sales...')
        
        sale_count = Sale.objects.count()
        Sale.objects.all().delete()
        
        self.stdout.write(
            self.style.WARNING(f'   Deleted {sale_count} sales')
        )

    def create_sales(self, count, days_back):
        """Create sample sales data"""
        self.stdout.write(f'💰 Creating {count} sales over {days_back} days...')
        
        products = list(Product.objects.all())
        if not products:
            raise CommandError('No products available for sales creation')

        # Get or create a default user for sales
        cashier = self.get_or_create_cashier()
        
        created_sales = []
        end_date = datetime.now()
        start_date = end_date - timedelta(days=days_back)

        for i in range(count):
            # Generate random sale date within the range
            random_date = self.random_date(start_date, end_date)
            
            # Create sale with temporary values (will be recalculated)
            sale = Sale.objects.create(
                cashier=cashier,
                payment_reference=f'CASH-{random.randint(1000000000, 9999999999)}',
                payment_status=True,  # All sample sales are paid
                subtotal=Decimal('0.00'),
                tax_amount=Decimal('0.00'),
                total_amount=Decimal('0.00'),
                created_at=random_date
            )

            # Add random products to the sale
            self.add_sale_items(sale, products)

            # Calculate totals
            self.calculate_sale_totals(sale)
            
            created_sales.append(sale)
            
            if (i + 1) % 10 == 0:
                self.stdout.write(f'   ✓ Created {i + 1}/{count} sales...')

        self.stdout.write(
            self.style.SUCCESS(f'💰 Created {len(created_sales)} sales')
        )

    def add_sale_items(self, sale, products):
        """Add random items to a sale"""
        # Random number of items per sale (1-8 items)
        num_items = random.randint(1, 8)
        
        # Select random products (no duplicates in same sale)
        selected_products = random.sample(products, min(num_items, len(products)))
        
        for product in selected_products:
            quantity = random.randint(1, 5)  # 1-5 quantity per item
            
            SaleItem.objects.create(
                sale=sale,
                product=product,
                quantity=quantity
            )

    def calculate_sale_totals(self, sale):
        """Calculate and update sale totals"""
        # The SaleItem model automatically calculates totals in its save method
        # and calls sale.calculate_totals(), so we just need to trigger it
        sale.calculate_totals()

    def get_or_create_cashier(self):
        """Get or create a default cashier user"""
        try:
            # Try to get an existing superuser
            cashier = User.objects.filter(is_superuser=True).first()
            if cashier:
                return cashier
            
            # Try to get any existing user
            cashier = User.objects.first()
            if cashier:
                return cashier
            
            # Create a default cashier user
            cashier = User.objects.create_user(
                username='sample_cashier',
                email='<EMAIL>',
                password='samplepass123',
                first_name='Sample',
                last_name='Cashier'
            )
            self.stdout.write('   ✓ Created sample cashier user')
            return cashier
            
        except Exception as e:
            raise CommandError(f'Failed to create cashier user: {str(e)}')

    def random_date(self, start_date, end_date):
        """Generate a random datetime between start_date and end_date"""
        time_between = end_date - start_date
        days_between = time_between.days
        random_days = random.randrange(days_between + 1)
        random_date = start_date + timedelta(days=random_days)
        
        # Add random time of day (business hours: 8 AM - 10 PM)
        random_hour = random.randint(8, 22)
        random_minute = random.randint(0, 59)
        random_second = random.randint(0, 59)
        
        return random_date.replace(
            hour=random_hour,
            minute=random_minute,
            second=random_second
        )

    def display_summary(self):
        """Display summary of created sales data"""
        from django.db import models

        total_sales = Sale.objects.count()
        total_revenue = Sale.objects.aggregate(
            total=models.Sum('total_amount')
        )['total'] or Decimal('0')
        
        avg_sale = total_revenue / total_sales if total_sales > 0 else Decimal('0')
        
        self.stdout.write('\n📊 Sales Summary:')
        self.stdout.write(f'   Total Sales: {total_sales}')
        self.stdout.write(f'   Total Revenue: ₱{total_revenue:,.2f}')
        self.stdout.write(f'   Average Sale: ₱{avg_sale:,.2f}')
        
        # Show recent sales
        recent_sales = Sale.objects.order_by('-created_at')[:5]
        self.stdout.write('\n🕒 Recent Sales:')
        for sale in recent_sales:
            self.stdout.write(
                f'   • Sale #{sale.id}: ₱{sale.total_amount:,.2f} '
                f'({sale.items.count()} items) - {sale.created_at.strftime("%Y-%m-%d %H:%M")}'
            )
        
        self.stdout.write('\n💡 Next steps:')
        self.stdout.write('   • Visit: http://localhost:8000/sales/ (to view sales)')
        self.stdout.write('   • Visit: http://localhost:8000/dashboard/ (to view analytics)')
        self.stdout.write('   • Visit: http://localhost:8000/sales/report/ (to view reports)')
