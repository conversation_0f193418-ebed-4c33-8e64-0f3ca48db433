#!/usr/bin/env python3
"""
Auto-startup Configuration for Django POS Desktop Application
Configures the POS system to start automatically when Windows boots.
"""

import os
import sys
import winreg
import shutil
from pathlib import Path
import logging

logger = logging.getLogger(__name__)

class AutoStartupManager:
    def __init__(self):
        self.app_name = "Django POS System"
        self.project_dir = Path(__file__).resolve().parent
        self.startup_script = self.project_dir / "system_tray.py"
        self.python_exe = sys.executable
        
        # Registry key for startup programs
        self.registry_key = r"SOFTWARE\Microsoft\Windows\CurrentVersion\Run"
        
    def create_startup_batch_file(self):
        """Create a batch file to start the POS system"""
        batch_content = f'''@echo off
cd /d "{self.project_dir}"
"{self.python_exe}" "{self.startup_script}"
'''
        
        batch_file = self.project_dir / "start_pos_system.bat"
        try:
            with open(batch_file, 'w') as f:
                f.write(batch_content)
            logger.info(f"Created startup batch file: {batch_file}")
            return batch_file
        except Exception as e:
            logger.error(f"Failed to create batch file: {e}")
            return None
    
    def create_startup_vbs_file(self):
        """Create a VBS file to start the POS system silently"""
        vbs_content = f'''Set WshShell = CreateObject("WScript.Shell")
WshShell.Run chr(34) & "{self.project_dir}\\start_pos_system.bat" & Chr(34), 0
Set WshShell = Nothing
'''
        
        vbs_file = self.project_dir / "start_pos_system.vbs"
        try:
            with open(vbs_file, 'w') as f:
                f.write(vbs_content)
            logger.info(f"Created startup VBS file: {vbs_file}")
            return vbs_file
        except Exception as e:
            logger.error(f"Failed to create VBS file: {e}")
            return None
    
    def add_to_registry_startup(self):
        """Add the application to Windows registry startup"""
        try:
            # Create batch and VBS files first
            batch_file = self.create_startup_batch_file()
            vbs_file = self.create_startup_vbs_file()
            
            if not batch_file or not vbs_file:
                return False
            
            # Open registry key
            key = winreg.OpenKey(
                winreg.HKEY_CURRENT_USER,
                self.registry_key,
                0,
                winreg.KEY_SET_VALUE
            )
            
            # Set the registry value to run our VBS file
            winreg.SetValueEx(
                key,
                self.app_name,
                0,
                winreg.REG_SZ,
                str(vbs_file)
            )
            
            winreg.CloseKey(key)
            logger.info("Added POS system to Windows startup (Registry)")
            return True
            
        except Exception as e:
            logger.error(f"Failed to add to registry startup: {e}")
            return False
    
    def remove_from_registry_startup(self):
        """Remove the application from Windows registry startup"""
        try:
            key = winreg.OpenKey(
                winreg.HKEY_CURRENT_USER,
                self.registry_key,
                0,
                winreg.KEY_SET_VALUE
            )
            
            winreg.DeleteValue(key, self.app_name)
            winreg.CloseKey(key)
            logger.info("Removed POS system from Windows startup (Registry)")
            return True
            
        except FileNotFoundError:
            logger.info("POS system was not in Windows startup")
            return True
        except Exception as e:
            logger.error(f"Failed to remove from registry startup: {e}")
            return False
    
    def add_to_startup_folder(self):
        """Add shortcut to Windows startup folder"""
        try:
            import win32com.client
            
            # Get startup folder path
            startup_folder = Path(os.environ['APPDATA']) / 'Microsoft' / 'Windows' / 'Start Menu' / 'Programs' / 'Startup'
            
            # Create batch and VBS files
            batch_file = self.create_startup_batch_file()
            vbs_file = self.create_startup_vbs_file()
            
            if not vbs_file:
                return False
            
            # Create shortcut
            shell = win32com.client.Dispatch("WScript.Shell")
            shortcut_path = startup_folder / f"{self.app_name}.lnk"
            shortcut = shell.CreateShortCut(str(shortcut_path))
            shortcut.Targetpath = str(vbs_file)
            shortcut.WorkingDirectory = str(self.project_dir)
            shortcut.IconLocation = str(vbs_file)
            shortcut.save()
            
            logger.info(f"Added shortcut to startup folder: {shortcut_path}")
            return True
            
        except ImportError:
            logger.warning("pywin32 not available, using registry method instead")
            return self.add_to_registry_startup()
        except Exception as e:
            logger.error(f"Failed to add to startup folder: {e}")
            return False
    
    def remove_from_startup_folder(self):
        """Remove shortcut from Windows startup folder"""
        try:
            startup_folder = Path(os.environ['APPDATA']) / 'Microsoft' / 'Windows' / 'Start Menu' / 'Programs' / 'Startup'
            shortcut_path = startup_folder / f"{self.app_name}.lnk"
            
            if shortcut_path.exists():
                shortcut_path.unlink()
                logger.info(f"Removed shortcut from startup folder: {shortcut_path}")
            
            return True
            
        except Exception as e:
            logger.error(f"Failed to remove from startup folder: {e}")
            return False
    
    def is_startup_enabled(self):
        """Check if auto-startup is currently enabled"""
        try:
            # Check registry
            key = winreg.OpenKey(
                winreg.HKEY_CURRENT_USER,
                self.registry_key,
                0,
                winreg.KEY_READ
            )
            
            try:
                value, _ = winreg.QueryValueEx(key, self.app_name)
                winreg.CloseKey(key)
                return True
            except FileNotFoundError:
                winreg.CloseKey(key)
                
                # Check startup folder
                startup_folder = Path(os.environ['APPDATA']) / 'Microsoft' / 'Windows' / 'Start Menu' / 'Programs' / 'Startup'
                shortcut_path = startup_folder / f"{self.app_name}.lnk"
                return shortcut_path.exists()
                
        except Exception as e:
            logger.error(f"Failed to check startup status: {e}")
            return False
    
    def enable_auto_startup(self):
        """Enable auto-startup for the POS system"""
        logger.info("Enabling auto-startup for POS system...")
        
        # Try registry method first, then startup folder
        if self.add_to_registry_startup():
            return True
        else:
            return self.add_to_startup_folder()
    
    def disable_auto_startup(self):
        """Disable auto-startup for the POS system"""
        logger.info("Disabling auto-startup for POS system...")
        
        # Remove from both registry and startup folder
        registry_result = self.remove_from_registry_startup()
        folder_result = self.remove_from_startup_folder()
        
        # Clean up batch and VBS files
        try:
            batch_file = self.project_dir / "start_pos_system.bat"
            vbs_file = self.project_dir / "start_pos_system.vbs"
            
            if batch_file.exists():
                batch_file.unlink()
            if vbs_file.exists():
                vbs_file.unlink()
                
        except Exception as e:
            logger.warning(f"Failed to clean up startup files: {e}")
        
        return registry_result or folder_result
    
    def create_desktop_shortcut(self):
        """Create a desktop shortcut for the POS system"""
        try:
            import win32com.client
            
            desktop = Path.home() / 'Desktop'
            shortcut_path = desktop / f"{self.app_name}.lnk"
            
            # Create VBS file if it doesn't exist
            vbs_file = self.create_startup_vbs_file()
            if not vbs_file:
                return False
            
            shell = win32com.client.Dispatch("WScript.Shell")
            shortcut = shell.CreateShortCut(str(shortcut_path))
            shortcut.Targetpath = str(vbs_file)
            shortcut.WorkingDirectory = str(self.project_dir)
            shortcut.IconLocation = str(vbs_file)
            shortcut.Description = "Django POS System Desktop Application"
            shortcut.save()
            
            logger.info(f"Created desktop shortcut: {shortcut_path}")
            return True
            
        except ImportError:
            logger.warning("pywin32 not available, cannot create desktop shortcut")
            return False
        except Exception as e:
            logger.error(f"Failed to create desktop shortcut: {e}")
            return False

def main():
    """Main function for auto-startup configuration"""
    import argparse
    
    parser = argparse.ArgumentParser(description="Configure auto-startup for Django POS System")
    parser.add_argument('--enable', action='store_true', help='Enable auto-startup')
    parser.add_argument('--disable', action='store_true', help='Disable auto-startup')
    parser.add_argument('--status', action='store_true', help='Check auto-startup status')
    parser.add_argument('--desktop-shortcut', action='store_true', help='Create desktop shortcut')
    
    args = parser.parse_args()
    
    # Setup logging
    logging.basicConfig(
        level=logging.INFO,
        format='%(asctime)s - %(levelname)s - %(message)s'
    )
    
    manager = AutoStartupManager()
    
    if args.enable:
        if manager.enable_auto_startup():
            print("✅ Auto-startup enabled successfully!")
        else:
            print("❌ Failed to enable auto-startup")
            
    elif args.disable:
        if manager.disable_auto_startup():
            print("✅ Auto-startup disabled successfully!")
        else:
            print("❌ Failed to disable auto-startup")
            
    elif args.status:
        if manager.is_startup_enabled():
            print("✅ Auto-startup is currently ENABLED")
        else:
            print("❌ Auto-startup is currently DISABLED")
            
    elif args.desktop_shortcut:
        if manager.create_desktop_shortcut():
            print("✅ Desktop shortcut created successfully!")
        else:
            print("❌ Failed to create desktop shortcut")
            
    else:
        print("Django POS System Auto-Startup Configuration")
        print("=" * 50)
        print("Current status:", "ENABLED" if manager.is_startup_enabled() else "DISABLED")
        print("\nOptions:")
        print("  --enable          Enable auto-startup")
        print("  --disable         Disable auto-startup")
        print("  --status          Check current status")
        print("  --desktop-shortcut Create desktop shortcut")

if __name__ == '__main__':
    main()
