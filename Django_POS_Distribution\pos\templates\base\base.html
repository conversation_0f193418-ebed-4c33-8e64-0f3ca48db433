<!DOCTYPE html>
<html lang="en">
    <head>
        <meta charset="UTF-8">
        <meta name="viewport" content="width=device-width, initial-scale=1.0">
        <title>{% block title %}POS System{% endblock %}</title>

    <!-- Tailwind CSS -->
        <script src="https://cdn.tailwindcss.com"></script>

    <!-- Google Fonts -->
        <link href="https://fonts.googleapis.com/css2?family=Inter:wght@400;500;600;700&display=swap" rel="stylesheet">

    <!-- Heroicons -->
        <script src="https://unpkg.com/@heroicons/v2/24/solid"></script>
        <script src="https://unpkg.com/heroicons@2.0.18/24/outline/index.js"></script>
        <link href="https://cdn.datatables.net/1.11.5/css/jquery.dataTables.min.css" rel="stylesheet">
        <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/5.15.4/css/all.min.css" rel="stylesheet">


    <!-- Enhanced Navigation CSS -->
        {% load static %}
        <link rel="stylesheet" href="{% static 'css/enhanced_navigation.css' %}">

    <!-- Custom Styles -->
        <style>
            body {
                font-family: 'Inter', sans-serif;
            }
        </style>
        {% block extra_css %}{% endblock %}
    </head>
    <body class="bg-gray-50">
        <div class="min-h-screen flex">
        <!-- Sidebar -->
            <aside class="hidden md:flex md:flex-shrink-0">
                <div class="flex flex-col w-64 sidebar-enhanced">
                    <div class="sidebar-brand">
                        <i class="fas fa-cash-register mr-2"></i>
                        POS Dashboard
                    </div>

                    <nav class="mt-6 flex-1 px-2 space-y-2">
                        <a href="{% url 'dashboard' %}" class="nav-link">
                            <i class="fas fa-tachometer-alt"></i>
                            Dashboard
                        </a>

                        <a href="{% url 'products' %}" class="nav-link">
                            <i class="fas fa-box"></i>
                            Products
                        </a>

                        <a href="{% url 'categories' %}" class="nav-link">
                            <i class="fas fa-tags"></i>
                            Categories
                        </a>

                        <a href="{% url 'sales' %}" class="nav-link">
                            <i class="fas fa-shopping-cart"></i>
                            Sales
                        </a>

                        <a href="{% url 'sales_report' %}" class="nav-link">
                            <i class="fas fa-chart-bar"></i>
                            Sales Report
                        </a>

                        <a href="{% url 'backup_dashboard' %}" class="nav-link">
                            <i class="fas fa-shield-alt"></i>
                            Data Backup
                        </a>
                    </nav>

                </div>
            </aside>

        <!-- Main Content -->
            <div class="flex-1 flex flex-col overflow-hidden">
            <!-- Top Navigation -->
                <header class="top-header">
                    <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
                        <div class="flex justify-between h-16">
                            <div class="flex">
                                <div class="flex-shrink-0 flex items-center">
                                <!-- Mobile menu button -->
                                    <button type="button" class="mobile-menu-button md:hidden">
                                        <i class="fas fa-bars text-xl"></i>
                                    </button>
                                    <!-- Page Title -->
                                    <h1 class="ml-4 text-xl font-bold text-gray-800">
                                        <i class="fas fa-store mr-2 text-blue-600"></i>
                                        Point of Sale System
                                    </h1>
                                </div>
                            </div>

                            <div class="flex items-center">
                                {% if user.is_authenticated %}
                                    <div class="ml-3 relative">
                                        <div class="relative">
                                            <button type="button" onclick="toggleDropdown()" class="user-dropdown-button flex items-center space-x-3">
                                                <span class="font-medium">{{ user.username }}</span>
                                                <div class="user-avatar">
                                                    <i class="fas fa-user"></i>
                                                </div>
                                            </button>
                                            <div id="userDropdown" class="dropdown-menu hidden absolute right-0 mt-2 w-48">
                                                <div class="py-1" role="menu" aria-orientation="vertical">
                                                    <div class="px-4 py-2 text-sm text-gray-500 border-b bg-gray-50">
                                                        <div class="flex items-center">
                                                            <i class="fas fa-user-shield text-blue-600 mr-2"></i>
                                                            Owner Account
                                                        </div>
                                                    </div>
                                                    <a href="{% url 'dashboard' %}" class="dropdown-item flex items-center" role="menuitem">
                                                        <i class="fas fa-tachometer-alt mr-2"></i>
                                                        Dashboard
                                                    </a>
                                                    <a href="{% url 'landing' %}" class="dropdown-item flex items-center" role="menuitem">
                                                        <i class="fas fa-home mr-2"></i>
                                                        Home
                                                    </a>
                                                    <div class="border-t">
                                                        <a href="{% url 'logout' %}" class="dropdown-item logout flex items-center" role="menuitem">
                                                            <i class="fas fa-sign-out-alt mr-2"></i>
                                                            Logout
                                                        </a>
                                                    </div>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                {% else %}
                                    <a href="{% url 'login' %}" class="bg-indigo-600 text-white px-4 py-2 rounded-lg hover:bg-indigo-700 transition-colors duration-200 flex items-center font-medium">
                                        <i class="fas fa-sign-in-alt mr-2"></i>
                                        Login
                                    </a>
                                {% endif %}
                            </div>
                        </div>
                    </div>
                    <script>
                        function toggleDropdown() {
                            const dropdown = document.getElementById('userDropdown');
                            dropdown.classList.toggle('hidden');
                            if (!dropdown.classList.contains('hidden')) {
                                dropdown.classList.add('opacity-100', 'scale-100');
                            } else {
                                dropdown.classList.remove('opacity-100', 'scale-100');
                            }
                        }

                        window.onclick = function(event) {
                            if (!event.target.closest('.relative')) {
                                var dropdown = document.getElementById('userDropdown');
                                if (!dropdown.classList.contains('hidden')) {
                                    dropdown.classList.add('hidden');
                                    dropdown.classList.remove('opacity-100', 'scale-100');
                                }
                            }
                        }
                    </script>
                </header>            <!-- Main Content Area -->
                <main class="flex-1 overflow-y-auto bg-gray-50">
                    <div class="py-6">
                        <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
                            {% block content %}{% endblock %}
                        </div>
                    </div>
                </main>
            </div>
        </div>

        {% block extra_js %}{% endblock %}
    </body>
</html>


