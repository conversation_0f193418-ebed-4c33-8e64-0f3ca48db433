{% extends 'base/base.html' %}
{% load humanize %}
{% load static %}
{% block title %}Sales - POS System{% endblock %}

{% block content %}
    <div class="bg-white shadow overflow-hidden sm:rounded-lg">
        <div class="px-4 py-5 sm:px-6 flex justify-between items-center">
            <h1 class="text-2xl font-semibold text-gray-900 flex items-center">
                <i class="fas fa-receipt text-indigo-600 mr-3"></i>
                Sales History
            </h1>
            <div class="flex space-x-3">
                {% if sales %}
                    <button onclick="confirmDeleteAllSales()" class="flex items-center bg-red-600 text-white px-4 py-2 rounded-md hover:bg-red-700 transition duration-150 ease-in-out">
                        <i class="fas fa-trash-alt mr-2"></i>
                        <span>Delete All</span>
                    </button>
                {% endif %}
                <a href="/api/create/" class="flex items-center bg-indigo-600 text-white px-4 py-2 rounded-md hover:bg-indigo-700 transition duration-150 ease-in-out">
                    <i class="fas fa-plus mr-2"></i>
                    <span>New Sale</span>
                </a>
            </div>
        </div>
        <div class="px-4 py-3 bg-gray-50">
            <form id="searchForm" class="flex gap-4">
                <input
                    type="text"
                    name="search"
                    id="searchInput"
                    placeholder="Search by Sale ID or Date..."
                    class="flex-1 rounded-md border-gray-300 shadow-sm focus:border-indigo-500 focus:ring-indigo-500"
                >
                <button
                    type="submit"
                    class="inline-flex items-center px-4 py-2 bg-indigo-600 text-white rounded-md hover:bg-indigo-700"
                >
                    <i class="fas fa-search mr-2"></i>
                    Search
                </button>
            </form>
        </div>

        <div class="overflow-x-auto max-h-[70vh] scrollbar-thin scrollbar-thumb-gray-300 scrollbar-track-gray-100">
            <table class="min-w-full divide-y divide-gray-200">
                <thead class="bg-gray-50 sticky top-0 z-10">
                    <tr>

                        <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Sale ID</th>
                        <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Date</th>
                          {% comment %} <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Cashier</th> {% endcomment %}
                        <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Subtotal</th>
                        <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Tax</th>
                        <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Total</th>
                        <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Status</th>
                        <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Actions</th>
                          {% comment %} <th class="px-6 py-3 text-left">
                            <div class="flex items-center">
                                <input type="checkbox" id="selectAll" class="rounded border-gray-300 cursor-pointer hover:border-indigo-500 transition duration-150 ease-in-out">
                                <label for="selectAll" class="ml-2 text-xs font-medium text-gray-500">Select All</label>
                            </div>
                        </th> {% endcomment %}
                    </tr>
                </thead>
                <tbody class="bg-white divide-y divide-gray-200">
                    {% for sale in sales %}
                        <tr class="hover:bg-gray-50">
                            <td class="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900">
                                <i class="fas fa-receipt mr-1"></i>#{{ sale.id }}
                            </td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                                <i class="far fa-calendar-alt mr-1"></i>{{ sale.created_at|date:"Y-m-d H:i" }}
                            </td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                                ₱{{ sale.subtotal|intcomma }}
                            </td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                                ₱{{ sale.tax_amount|intcomma }}
                            </td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                                ₱{{ sale.total_amount|intcomma }}
                            </td>
                            <td class="px-6 py-4 whitespace-nowrap">
                                {% if sale.payment_status %}
                                    <span class="px-2 inline-flex text-xs leading-5 font-semibold rounded-full bg-green-100 text-green-800">
                                        <i class="fas fa-check-circle mr-1"></i>Paid
                                    </span>
                                {% else %}
                                    <span class="px-2 inline-flex text-xs leading-5 font-semibold rounded-full bg-red-100 text-red-800">
                                        <i class="fas fa-clock mr-1"></i>Pending
                                    </span>
                                {% endif %}
                            </td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm font-medium">
                                <div class="flex space-x-3">
                                    <a href="{% url 'sale_details' sale.id %}" class="flex items-center text-indigo-600 hover:text-indigo-900 transition duration-150 ease-in-out">
                                        <i class="fas fa-eye mr-1"></i>View
                                    </a>
                                    <button onclick="reprintReceipt({{ sale.id }})" class="flex items-center text-green-600 hover:text-green-900 transition duration-150 ease-in-out">
                                        <i class="fas fa-print mr-1"></i>Print
                                    </button>
                                </div>
                            </td>
                        </tr>
                    {% endfor %}
                </tbody>
            </table>
        </div>        <div class="px-4 py-3 bg-gray-50 flex items-center justify-between border-t border-gray-200 sm:px-6">
            <div class="flex-1 flex justify-between sm:hidden">
                {% if sales.has_previous %}
                    <a href="?page={{ sales.previous_page_number }}" class="relative inline-flex items-center px-4 py-2 border border-gray-300 text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50">Previous</a>
                {% endif %}
                {% if sales.has_next %}
                    <a href="?page={{ sales.next_page_number }}" class="ml-3 relative inline-flex items-center px-4 py-2 border border-gray-300 text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50">Next</a>
                {% endif %}
            </div>
            <div class="hidden sm:flex-1 sm:flex sm:items-center sm:justify-between">
                <div class="flex items-center justify-between w-full">
                    <p class="text-sm text-gray-700">
                        Showing
                        <span class="font-medium">{{ sales.start_index }}</span>
                        to
                        <span class="font-medium">{{ sales.end_index }}</span>
                        of
                        <span class="font-medium">{{ sales.paginator.count }}</span>
                        results
                    </p>

                </div>
                <div>
                    <nav class="relative z-0 inline-flex items-center space-x-4" aria-label="Pagination">
                        <div class="flex items-center space-x-1 bg-white rounded-lg shadow-sm">
                            {% if sales.has_previous %}
                                <a href="?page={{ sales.previous_page_number }}" class="relative inline-flex items-center px-3 py-2 rounded-l-lg border border-gray-300 bg-white text-sm font-medium text-gray-500 hover:bg-gray-50 hover:text-indigo-600 transition-colors duration-200">
                                    <span class="sr-only">Previous</span>
                                    <i class="fas fa-chevron-left"></i>
                                </a>
                            {% endif %}

                            {% for i in sales.paginator.page_range %}
                                {% if sales.number == i %}
                                    <span class="relative inline-flex items-center px-4 py-2 border border-indigo-500 bg-indigo-50 text-sm font-medium text-indigo-600 shadow-sm">{{ i }}</span>
                                {% else %}
                                    <a href="?page={{ i }}" class="relative inline-flex items-center px-4 py-2 border border-gray-300 bg-white text-sm font-medium text-gray-700 hover:bg-gray-50 hover:text-indigo-600 hover:border-indigo-300 transition-colors duration-200">{{ i }}</a>
                                {% endif %}
                            {% endfor %}

                            {% if sales.has_next %}
                                <a href="?page={{ sales.next_page_number }}" class="relative inline-flex items-center px-3 py-2 rounded-r-lg border border-gray-300 bg-white text-sm font-medium text-gray-500 hover:bg-gray-50 hover:text-indigo-600 transition-colors duration-200">
                                    <span class="sr-only">Next</span>
                                    <i class="fas fa-chevron-right"></i>
                                </a>
                            {% endif %}
                        </div>
                        {% comment %} <button onclick="bulkDelete()" class="flex items-center bg-red-600 text-white px-4 py-2 rounded-lg hover:bg-red-700 transition-all duration-200 transform hover:scale-105 shadow-md">
                            <i class="fas fa-trash-alt mr-2"></i>
                            <span>Delete Selected</span>
                        </button> {% endcomment %}
                    </nav>
                </div>
            </div>
        </div>
    </div>

    <script>
        // Delete All Sales Function
        function confirmDeleteAllSales() {
            // Get sales count from the page
            const salesRows = document.querySelectorAll('tbody tr');
            const salesCount = salesRows.length;

            if (salesCount === 0) {
                alert('No sales to delete.');
                return;
            }

            const confirmMessage = `🚨 EXTREME WARNING: This will permanently delete ALL ${salesCount} sales records!\n\n` +
                                 `This action will:\n` +
                                 `• Remove all sales data and transaction history\n` +
                                 `• Delete all associated sale items\n` +
                                 `• Permanently erase financial records\n` +
                                 `• Cannot be undone or recovered\n\n` +
                                 `⚠️ This is a DESTRUCTIVE operation that will affect your business records!\n\n` +
                                 `Are you absolutely certain you want to continue?`;

            if (confirm(confirmMessage)) {
                // Triple confirmation for sales data (most critical)
                const secondConfirm = confirm(`🔥 CRITICAL CONFIRMATION\n\nYou are about to delete ${salesCount} sales records.\n\nThis will permanently remove all transaction history and cannot be recovered.\n\nDo you really want to proceed?`);

                if (secondConfirm) {
                    const finalConfirm = confirm(`⚠️ FINAL WARNING ⚠️\n\nLast chance to cancel!\n\nDeleting all sales data will:\n• Remove financial history\n• Affect reporting and analytics\n• Cannot be undone\n\nType 'DELETE' in the next prompt to confirm.`);

                    if (finalConfirm) {
                        const typeConfirm = prompt('Type "DELETE" to confirm deletion of all sales:');

                        if (typeConfirm === 'DELETE') {
                            // Show loading state
                            const deleteBtn = event.target;
                            const originalText = deleteBtn.innerHTML;
                            deleteBtn.innerHTML = '<i class="fas fa-spinner fa-spin mr-2"></i>Deleting...';
                            deleteBtn.disabled = true;

                            // Create and submit form
                            const form = document.createElement('form');
                            form.method = 'POST';
                            form.action = '{% url "delete_all_sales" %}';

                            const csrfToken = document.createElement('input');
                            csrfToken.type = 'hidden';
                            csrfToken.name = 'csrfmiddlewaretoken';
                            csrfToken.value = '{{ csrf_token }}';
                            form.appendChild(csrfToken);

                            document.body.appendChild(form);
                            form.submit();
                        } else {
                            alert('Deletion cancelled. You must type "DELETE" exactly to confirm.');
                        }
                    }
                }
            }
        }
    </script>

    <script src="{% static 'js/receipt.js' %}"></script>
{% endblock %}


