/* Enhanced Navigation Styles for Better Visibility */

/* Sidebar Improvements */
.sidebar-enhanced {
    background: linear-gradient(180deg, #1f2937 0%, #111827 100%);
    border-right: 3px solid #3b82f6;
    box-shadow: 4px 0 15px rgba(0, 0, 0, 0.1);
}

.sidebar-brand {
    background: linear-gradient(135deg, #3b82f6 0%, #1d4ed8 100%);
    color: white;
    font-weight: 700;
    font-size: 1.25rem;
    padding: 1rem;
    text-align: center;
    border-bottom: 2px solid #1d4ed8;
    box-shadow: 0 2px 10px rgba(59, 130, 246, 0.3);
}

/* Navigation Links */
.nav-link {
    color: #e5e7eb !important;
    font-weight: 500;
    font-size: 0.95rem;
    padding: 0.75rem 1rem;
    margin: 0.25rem 0.5rem;
    border-radius: 0.5rem;
    transition: all 0.3s ease;
    border: 1px solid transparent;
    display: flex;
    align-items: center;
}

.nav-link:hover {
    background: linear-gradient(135deg, #3b82f6 0%, #2563eb 100%) !important;
    color: white !important;
    transform: translateX(5px);
    border: 1px solid #60a5fa;
    box-shadow: 0 4px 15px rgba(59, 130, 246, 0.4);
}

.nav-link.active {
    background: linear-gradient(135deg, #059669 0%, #047857 100%) !important;
    color: white !important;
    border: 1px solid #10b981;
    box-shadow: 0 4px 15px rgba(16, 185, 129, 0.4);
}

.nav-link svg {
    width: 1.25rem;
    height: 1.25rem;
    margin-right: 0.75rem;
    transition: transform 0.3s ease;
}

.nav-link:hover svg {
    transform: scale(1.1);
}

/* Top Header Improvements */
.top-header {
    background: linear-gradient(135deg, #ffffff 0%, #f8fafc 100%);
    border-bottom: 3px solid #e2e8f0;
    box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);
}

/* User Dropdown */
.user-dropdown-button {
    background: linear-gradient(135deg, #f8fafc 0%, #e2e8f0 100%);
    border: 2px solid #cbd5e1;
    color: #374151;
    font-weight: 600;
    padding: 0.5rem 1rem;
    border-radius: 0.75rem;
    transition: all 0.3s ease;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.user-dropdown-button:hover {
    background: linear-gradient(135deg, #3b82f6 0%, #2563eb 100%);
    color: white;
    border-color: #3b82f6;
    transform: translateY(-2px);
    box-shadow: 0 6px 20px rgba(59, 130, 246, 0.3);
}

.user-avatar {
    background: linear-gradient(135deg, #3b82f6 0%, #1d4ed8 100%);
    padding: 0.5rem;
    border-radius: 50%;
    color: white;
    transition: all 0.3s ease;
}

.user-dropdown-button:hover .user-avatar {
    background: linear-gradient(135deg, #ffffff 0%, #f1f5f9 100%);
    color: #3b82f6;
    transform: rotate(360deg);
}

/* Dropdown Menu */
.dropdown-menu {
    background: white;
    border: 2px solid #e2e8f0;
    border-radius: 0.75rem;
    box-shadow: 0 10px 40px rgba(0, 0, 0, 0.15);
    overflow: hidden;
    z-index: 9999;
    position: absolute;
}

.dropdown-item {
    color: #374151;
    padding: 0.75rem 1rem;
    transition: all 0.2s ease;
    border-bottom: 1px solid #f1f5f9;
}

.dropdown-item:hover {
    background: linear-gradient(135deg, #3b82f6 0%, #2563eb 100%);
    color: white;
    transform: translateX(5px);
}

.dropdown-item.logout:hover {
    background: linear-gradient(135deg, #ef4444 0%, #dc2626 100%);
}

/* Mobile Menu Button */
.mobile-menu-button {
    color: #6b7280;
    padding: 0.5rem;
    border-radius: 0.5rem;
    transition: all 0.3s ease;
    border: 2px solid transparent;
}

.mobile-menu-button:hover {
    color: #3b82f6;
    background: #f3f4f6;
    border-color: #3b82f6;
    transform: scale(1.1);
}

/* Active Page Indicator */
.nav-link.current-page {
    background: linear-gradient(135deg, #059669 0%, #047857 100%) !important;
    color: white !important;
    border: 1px solid #10b981;
    box-shadow: 0 4px 15px rgba(16, 185, 129, 0.4);
    position: relative;
}

.nav-link.current-page::before {
    content: '';
    position: absolute;
    left: -0.5rem;
    top: 50%;
    transform: translateY(-50%);
    width: 4px;
    height: 60%;
    background: #10b981;
    border-radius: 2px;
}

/* Responsive Improvements */
@media (max-width: 768px) {
    .sidebar-enhanced {
        transform: translateX(-100%);
        transition: transform 0.3s ease;
    }
    
    .sidebar-enhanced.mobile-open {
        transform: translateX(0);
    }
}

/* Animation for page transitions */
.nav-link {
    position: relative;
    overflow: hidden;
}

.nav-link::after {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
    transition: left 0.5s ease;
}

.nav-link:hover::after {
    left: 100%;
}

/* Focus states for accessibility */
.nav-link:focus {
    outline: 2px solid #3b82f6;
    outline-offset: 2px;
}

.user-dropdown-button:focus {
    outline: 2px solid #3b82f6;
    outline-offset: 2px;
}
