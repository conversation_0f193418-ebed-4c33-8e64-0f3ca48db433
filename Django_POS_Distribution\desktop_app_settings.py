"""
Desktop Application Settings for Django POS System
Optimized settings for desktop deployment with enhanced security and performance.
"""

from pos_system.settings import *
import os
from pathlib import Path

# Desktop application specific settings
DESKTOP_MODE = True

# Security settings for desktop deployment
DEBUG = False  # Disable debug mode for desktop
ALLOWED_HOSTS = ['127.0.0.1', 'localhost', '0.0.0.0']

# Generate a more secure secret key for desktop deployment
SECRET_KEY = os.environ.get('DJANGO_SECRET_KEY', SECRET_KEY)

# Database optimization for desktop
DATABASES = {
    'default': {
        'ENGINE': 'django.db.backends.sqlite3',
        'NAME': BASE_DIR / 'pos_desktop.db',
        'OPTIONS': {
            'timeout': 20,
            'check_same_thread': False,
        }
    }
}

# Static files configuration for desktop
STATIC_URL = '/static/'
STATIC_ROOT = BASE_DIR / 'desktop_static'
STATICFILES_DIRS = [
    BASE_DIR / 'static',
]

# Media files for desktop
MEDIA_URL = '/media/'
MEDIA_ROOT = BASE_DIR / 'desktop_media'

# Logging configuration for desktop application
LOGGING = {
    'version': 1,
    'disable_existing_loggers': False,
    'formatters': {
        'verbose': {
            'format': '{levelname} {asctime} {module} {process:d} {thread:d} {message}',
            'style': '{',
        },
        'simple': {
            'format': '{levelname} {message}',
            'style': '{',
        },
    },
    'handlers': {
        'file': {
            'level': 'INFO',
            'class': 'logging.FileHandler',
            'filename': BASE_DIR / 'logs' / 'pos_desktop.log',
            'formatter': 'verbose',
        },
        'console': {
            'level': 'INFO',
            'class': 'logging.StreamHandler',
            'formatter': 'simple',
        },
    },
    'root': {
        'handlers': ['file', 'console'],
        'level': 'INFO',
    },
    'loggers': {
        'django': {
            'handlers': ['file', 'console'],
            'level': 'INFO',
            'propagate': False,
        },
        'pos': {
            'handlers': ['file', 'console'],
            'level': 'INFO',
            'propagate': False,
        },
    },
}

# Session configuration for desktop
SESSION_COOKIE_AGE = 86400  # 24 hours
SESSION_SAVE_EVERY_REQUEST = True
SESSION_EXPIRE_AT_BROWSER_CLOSE = False

# Cache configuration for better performance
CACHES = {
    'default': {
        'BACKEND': 'django.core.cache.backends.locmem.LocMemCache',
        'LOCATION': 'pos-desktop-cache',
        'TIMEOUT': 300,
        'OPTIONS': {
            'MAX_ENTRIES': 1000,
        }
    }
}

# Disable unnecessary middleware for desktop
MIDDLEWARE = [
    'django.middleware.security.SecurityMiddleware',
    'django.contrib.sessions.middleware.SessionMiddleware',
    'django.middleware.common.CommonMiddleware',
    'django.middleware.csrf.CsrfViewMiddleware',
    'django.contrib.auth.middleware.AuthenticationMiddleware',
    'django.contrib.messages.middleware.MessageMiddleware',
    'django.middleware.clickjacking.XFrameOptionsMiddleware',
]

# Desktop-specific security settings
SECURE_BROWSER_XSS_FILTER = True
SECURE_CONTENT_TYPE_NOSNIFF = True
X_FRAME_OPTIONS = 'DENY'

# Disable some security features that are not needed for local desktop app
SECURE_SSL_REDIRECT = False
SECURE_HSTS_SECONDS = 0
SECURE_HSTS_INCLUDE_SUBDOMAINS = False
SECURE_HSTS_PRELOAD = False

# Email configuration (disabled for desktop)
EMAIL_BACKEND = 'django.core.mail.backends.console.EmailBackend'

# Internationalization
USE_I18N = True
USE_L10N = True
USE_TZ = True

# Desktop application metadata
DESKTOP_APP_INFO = {
    'name': 'Django POS System',
    'version': '1.0.0',
    'description': 'Point of Sale System Desktop Application',
    'author': 'POS System Developer',
    'url': 'http://127.0.0.1:8000',
}

# Performance optimizations for desktop
DATA_UPLOAD_MAX_MEMORY_SIZE = 10 * 1024 * 1024  # 10MB
FILE_UPLOAD_MAX_MEMORY_SIZE = 10 * 1024 * 1024  # 10MB

# Disable admin documentation for desktop
INSTALLED_APPS = [app for app in INSTALLED_APPS if app != 'django.contrib.admindocs']

# Custom settings for POS desktop features
POS_DESKTOP_SETTINGS = {
    'AUTO_BACKUP': True,
    'BACKUP_INTERVAL_HOURS': 24,
    'MAX_BACKUP_FILES': 7,
    'ENABLE_SYSTEM_TRAY': True,
    'AUTO_START_SERVER': True,
    'DEFAULT_PORT': 8000,
    'BROWSER_AUTO_OPEN': True,
    'MINIMIZE_TO_TRAY': True,
}

# Create necessary directories
def create_desktop_directories():
    """Create necessary directories for desktop application"""
    directories = [
        BASE_DIR / 'logs',
        BASE_DIR / 'desktop_static',
        BASE_DIR / 'desktop_media',
        BASE_DIR / 'backups',
    ]
    
    for directory in directories:
        directory.mkdir(exist_ok=True)

# Create directories when settings are imported
create_desktop_directories()
