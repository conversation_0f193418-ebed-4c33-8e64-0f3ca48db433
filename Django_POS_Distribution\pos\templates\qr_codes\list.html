{% extends 'base/base.html' %}

{% block title %}QR Codes - POS System{% endblock %}

{% block content %}
    <div class="bg-white shadow overflow-hidden sm:rounded-lg">
        <div class="px-4 py-5 sm:px-6 flex justify-between items-center">
            <h1 class="text-2xl font-semibold text-gray-900">Payment QR Codes</h1>
            <button class="bg-indigo-600 text-white px-4 py-2 rounded-md hover:bg-indigo-700">
                Add QR Code
            </button>
        </div>

        <div class="grid grid-cols-1 gap-6 sm:grid-cols-2 lg:grid-cols-3 p-6">
            {% for qr in qr_codes %}
                <div class="bg-white overflow-hidden shadow-sm rounded-lg">
                    <div class="p-6">
                        <div class="flex items-center justify-between mb-4">
                            <h3 class="text-lg font-medium text-gray-900">{{ qr.name }}</h3>
                            <span class="px-2 py-1 text-xs font-semibold rounded-full {% if qr.is_active %}bg-green-100 text-green-800{% else %}bg-red-100 text-red-800{% endif %}">
                                {{ qr.is_active|yesno:"Active,Inactive" }}
                            </span>
                        </div>
                        <div class="flex justify-center mb-4">
                            <img src="{{ qr.qr_image.url }}" alt="{{ qr.name }}" class="h-48 w-48 object-contain">
                        </div>
                        <div class="flex justify-end space-x-3">
                            <button class="text-indigo-600 hover:text-indigo-900">Edit</button>
                            <button class="text-red-600 hover:text-red-900">Delete</button>
                        </div>
                    </div>
                </div>
            {% endfor %}
        </div>
    </div>
{% endblock %}
