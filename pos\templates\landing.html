<!DOCTYPE html>
<html lang="en">
    <head>
        <meta charset="UTF-8">
        <meta name="viewport" content="width=device-width, initial-scale=1.0">
        <title>Welcome to POS System</title>
        <link rel="icon" type="image/svg+xml" href="data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24'%3E%3Cpath fill='%234F46E5' d='M4 4h16a2 2 0 0 1 2 2v12a2 2 0 0 1-2 2H4a2 2 0 0 1-2-2V6a2 2 0 0 1 2-2zm0 2v12h16V6H4zm8 3a2 2 0 1 1 0 4 2 2 0 0 1 0-4zm0 6a4 4 0 1 0 0-8 4 4 0 0 0 0 8z'/%3E%3C/svg%3E">
        <script src="https://cdn.tailwindcss.com"></script>
        <link href="https://fonts.googleapis.com/css2?family=Poppins:wght@400;500;600;700&display=swap" rel="stylesheet">
        <style>
            body {
                font-family: 'Poppins', sans-serif;
            }
            .hero-gradient {
                background: linear-gradient(135deg, #EBF4FF 0%, #C3DAFE 100%);
            }
            .feature-card:hover {
                transform: translateY(-5px);
            }

            /* Enhanced Landing Navigation */
            .landing-nav {
                background: linear-gradient(135deg, rgba(59, 130, 246, 0.95) 0%, rgba(29, 78, 216, 0.95) 100%);
                backdrop-filter: blur(10px);
                border-bottom: 2px solid rgba(255, 255, 255, 0.2);
                box-shadow: 0 4px 20px rgba(0, 0, 0, 0.1);
            }

            .nav-brand {
                color: white;
                font-weight: 700;
                font-size: 1.5rem;
                text-shadow: 0 2px 4px rgba(0, 0, 0, 0.3);
            }

            .nav-link-landing {
                background: linear-gradient(135deg, rgba(255, 255, 255, 0.2) 0%, rgba(255, 255, 255, 0.1) 100%);
                border: 2px solid rgba(255, 255, 255, 0.3);
                color: white;
                font-weight: 600;
                padding: 0.75rem 1.5rem;
                border-radius: 0.75rem;
                transition: all 0.3s ease;
                backdrop-filter: blur(5px);
                text-shadow: 0 1px 2px rgba(0, 0, 0, 0.3);
                box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
            }

            .nav-link-landing:hover {
                background: linear-gradient(135deg, rgba(255, 255, 255, 0.3) 0%, rgba(255, 255, 255, 0.2) 100%);
                border-color: rgba(255, 255, 255, 0.5);
                transform: translateY(-2px);
                box-shadow: 0 6px 20px rgba(0, 0, 0, 0.2);
                color: white;
            }

            .nav-link-login {
                background: linear-gradient(135deg, #10b981 0%, #047857 100%);
                border: 2px solid #10b981;
                color: white;
                font-weight: 700;
                padding: 0.75rem 1.5rem;
                border-radius: 0.75rem;
                transition: all 0.3s ease;
                text-shadow: 0 1px 2px rgba(0, 0, 0, 0.3);
                box-shadow: 0 4px 15px rgba(16, 185, 129, 0.3);
            }

            .nav-link-login:hover {
                background: linear-gradient(135deg, #059669 0%, #065f46 100%);
                border-color: #059669;
                transform: translateY(-2px);
                box-shadow: 0 8px 25px rgba(16, 185, 129, 0.4);
                color: white;
            }

            .nav-link-logout {
                background: linear-gradient(135deg, #ef4444 0%, #dc2626 100%);
                border: 2px solid #ef4444;
                color: white;
                font-weight: 600;
                padding: 0.75rem 1.5rem;
                border-radius: 0.75rem;
                transition: all 0.3s ease;
                text-shadow: 0 1px 2px rgba(0, 0, 0, 0.3);
                box-shadow: 0 4px 15px rgba(239, 68, 68, 0.3);
            }

            .nav-link-logout:hover {
                background: linear-gradient(135deg, #dc2626 0%, #b91c1c 100%);
                border-color: #dc2626;
                transform: translateY(-2px);
                box-shadow: 0 8px 25px rgba(239, 68, 68, 0.4);
                color: white;
            }
        </style>
    </head>
    <body>
        <div class="min-h-screen hero-gradient">
            <!-- Navigation Header -->
            <nav class="landing-nav relative z-10">
                <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
                    <div class="flex justify-between items-center py-6">
                        <!-- Logo/Brand -->
                        <div class="flex items-center">
                            <div class="flex-shrink-0 flex items-center">
                                <i class="fas fa-cash-register text-3xl text-white mr-3 drop-shadow-lg"></i>
                                <span class="nav-brand">POS System</span>
                            </div>
                        </div>

                        <!-- Navigation Links -->
                        <div class="flex items-center space-x-4">
                            {% if user.is_authenticated %}
                                <a href="{% url 'dashboard' %}" class="nav-link-landing flex items-center">
                                    <i class="fas fa-tachometer-alt mr-2"></i>
                                    Dashboard
                                </a>
                                <a href="{% url 'logout' %}" class="nav-link-logout flex items-center">
                                    <i class="fas fa-sign-out-alt mr-2"></i>
                                    Logout
                                </a>
                            {% else %}
                                <a href="{% url 'login' %}" class="nav-link-login flex items-center">
                                    <i class="fas fa-user-shield mr-2"></i>
                                    Owner Login
                                </a>
                            {% endif %}
                        </div>
                    </div>
                </div>
            </nav>

            <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 pt-12">
                <div class="text-center">
                    <h1 class="text-5xl tracking-tight font-extrabold text-gray-900 sm:text-6xl md:text-7xl">
                        <span class="block bg-clip-text text-transparent bg-gradient-to-r from-blue-600 to-indigo-600">Smart Shopping Experience</span>
                        <span class="block text-blue-600 mt-2">With Our POS System</span>
                    </h1>
                    <p class="mt-6 max-w-md mx-auto text-lg text-gray-600 sm:text-xl md:mt-8 md:text-2xl md:max-w-3xl">
                        Experience seamless shopping with automatic tax calculation, QR code payments, and organized product categories. Browse our extensive inventory with ease.
                    </p>

                    <!-- Adding new how-to-use section -->
                    <div class="mt-8 max-w-2xl mx-auto">
                        <div class="bg-white/80 backdrop-blur-sm rounded-2xl p-6 shadow-lg">
                            <h3 class="text-xl font-bold text-blue-600 mb-4">👋 Here's How It Works:</h3>
                            <ol class="space-y-3 text-left text-gray-700">
                                <li class="flex items-center">
                                    <span class="bg-blue-600 text-white rounded-full w-6 h-6 flex items-center justify-center mr-3">1</span>
                                    Click "Start Shopping Now!" to browse our products
                                </li>
                                <li class="flex items-center">
                                    <span class="bg-blue-600 text-white rounded-full w-6 h-6 flex items-center justify-center mr-3">2</span>
                                    Add items to your cart - prices include 12% tax automatically
                                </li>
                                <li class="flex items-center">
                                    <span class="bg-blue-600 text-white rounded-full w-6 h-6 flex items-center justify-center mr-3">3</span>
                                    When ready, proceed to checkout and pay with cash
                                </li>
                                <li class="flex items-center">
                                    <span class="bg-blue-600 text-white rounded-full w-6 h-6 flex items-center justify-center mr-3">4</span>
                                    Get your receipt and collect your items
                                </li>
                            </ol>
                        </div>
                    </div>
                    <div class="mt-8 max-w-md mx-auto sm:flex sm:justify-center md:mt-12">
                        <div class="relative">
                            <div class="rounded-md shadow-lg relative group">
                                <!-- Add a pulsing effect behind the button -->
                                <div class="absolute -inset-1 bg-gradient-to-r from-blue-600 to-indigo-600 rounded-xl blur opacity-30 group-hover:opacity-100 transition duration-1000 group-hover:duration-200"></div>

                                <!-- Enhanced button with shine effect -->
                                <a href="/api/create/" class="relative w-full flex items-center justify-center px-10 py-4 border border-transparent text-lg font-semibold rounded-xl text-white bg-gradient-to-r from-blue-600 to-indigo-600 hover:from-blue-700 hover:to-indigo-700 transform transition duration-300 hover:scale-105 hover:shadow-xl">
                                    <span class="absolute inset-0 overflow-hidden rounded-xl">
                                        <span class="absolute inset-0 transform -translate-x-full hover:translate-x-full transition-all duration-500 bg-gradient-to-r from-transparent via-white/20 to-transparent group-hover:animate-[shine_1s_ease-in-out]"></span>
                                    </span>
                                    <svg class="mr-3 w-6 h-6 animate-bounce" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 3h2l.4 2M7 13h10l4-8H5.4M7 13L5.4 5M7 13l-2.293 2.293c-.63.63-.184 1.707.707 1.707H17m0 0a2 2 0 100 4 2 2 0 000-4zm-8 2a2 2 0 11-4 0 2 2 0 014 0z" />
                                    </svg>
                                    Start Shopping Now!
                                    <svg class="ml-3 -mr-1 w-6 h-6 animate-pulse" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 7l5 5m0 0l-5 5m5-5H6" />
                                    </svg>
                                </a>
                            </div>
                        </div>
                    </div>
                </div>
            <!-- Feature Section -->
                <div class="mt-24 grid grid-cols-1 gap-10 sm:grid-cols-2 lg:grid-cols-3">
                    <div class="bg-white rounded-xl shadow-xl p-8 hover:shadow-2xl transition duration-300 feature-card">
                        <div class="text-blue-600 mb-6">
                            <svg class="h-16 w-16" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8c-1.657 0-3 .895-3 2s1.343 2 3 2 3 .895 3 2-1.343 2-3 2m0-8c1.11 0 2.08.402 2.599 1M12 8V7m0 1v8m0 0v1m0-1c-1.11 0-2.08-.402-2.599-1M21 12a9 9 0 11-18 0 9 9 0 0118 0z"/>
                            </svg>
                        </div>
                        <h3 class="text-2xl font-bold mb-4 text-gray-800">Automatic Tax Calculation</h3>
                        <p class="text-gray-600 text-lg">12% tax automatically calculated on all purchases for transparency.</p>
                    </div>

                    <div class="bg-white rounded-xl shadow-xl p-8 hover:shadow-2xl transition duration-300 feature-card">
                        <div class="text-blue-600 mb-6">
                            <svg class="h-16 w-16" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M17 9V7a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2m2 4h10a2 2 0 002-2v-6a2 2 0 00-2-2H9a2 2 0 00-2 2v6a2 2 0 002 2zm7-5a2 2 0 11-4 0 2 2 0 014 0z"/>
                            </svg>
                        </div>
                        <h3 class="text-2xl font-bold mb-4 text-gray-800">Cash Payments</h3>
                        <p class="text-gray-600 text-lg">Simple and reliable cash transactions with automatic change calculation.</p>
                    </div>

                    <div class="bg-white rounded-xl shadow-xl p-8 hover:shadow-2xl transition duration-300 feature-card">
                        <div class="text-blue-600 mb-6">
                            <svg class="h-16 w-16" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 10h18M3 14h18m-9-4v8m-7 0h14a2 2 0 002-2V8a2 2 0 00-2-2H5a2 2 0 00-2 2v8a2 2 0 002 2z"/>
                            </svg>
                        </div>
                        <h3 class="text-2xl font-bold mb-4 text-gray-800">Organized Categories</h3>
                        <p class="text-gray-600 text-lg">Products neatly organized by categories for easy browsing and selection.</p>
                    </div>
                </div>
            </div>
        </div>

    </body>
</html>


