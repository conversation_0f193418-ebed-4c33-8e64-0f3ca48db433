# Django POS System - Desktop Application

A professional Point of Sale system that runs as a desktop application.

## Quick Start

### For End Users:
1. Use the files in `Django_POS_Distribution/` folder
2. Run `SETUP.bat` as administrator (one-time setup)
3. Double-click desktop shortcut to start POS system

### For Developers:
1. Install dependencies: `uv pip install -r requirements_desktop.txt`
2. Run: `python desktop_launcher.py`

## Project Structure

```
Django_POS_System/
├── Django_POS_Distribution/     # Ready-to-distribute package
├── pos/                         # Main POS application
├── pos_system/                  # Django project settings
├── static/                      # Static files
├── docs/                        # Documentation
├── development_tools/           # Development utilities
├── desktop_launcher.py          # Main desktop launcher
├── system_tray.py              # System tray integration
├── auto_startup.py             # Auto-startup configuration
└── requirements_desktop.txt    # Dependencies
```

## Features

- Complete Point of Sale system
- Desktop application experience
- System tray integration
- Auto-startup capability
- Professional installation
- Multiple launcher options

## Documentation

See `docs/` folder for detailed documentation:
- `README_DESKTOP.md` - Desktop application guide
- `LOGIN_SETUP.md` - User authentication setup
- `DEMO_SETUP_COMMANDS.md` - Demo data setup
- `DELETE_ALL_IMPLEMENTATION.md` - Bulk operations guide

## Development

See `development_tools/` folder for development utilities:
- `setup_desktop.py` - Automated desktop setup
- `create_owner.py` - Create owner accounts

## Distribution

The `Django_POS_Distribution/` folder contains everything needed for end users:
- Complete POS application
- Installation scripts
- User documentation
- Multiple launcher options

Simply zip this folder and share with users.

## Success!

Your Django POS system is now a professional desktop application!