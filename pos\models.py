from django.db import models
from django.contrib.auth.models import User
from decimal import Decimal

class Category(models.Model):
    name = models.CharField(max_length=100)
    created_at = models.DateTimeField(auto_now_add=True)
    
    def __str__(self):
        return self.name
    
    class Meta:
        verbose_name_plural = "Categories"

class Product(models.Model):
    name = models.CharField(max_length=200)
    barcode = models.CharField(max_length=100, unique=True)
    description = models.TextField(blank=True)
    category = models.ForeignKey(Category, on_delete=models.CASCADE, related_name='products')
    price = models.DecimalField(max_digits=10, decimal_places=2)
    stock_quantity = models.IntegerField(default=0)
    expiration_date = models.DateField(null=True, blank=True)
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)    
    @property
    def price_with_tax(self):
        tax_rate = Decimal('0.12')
        tax_amount = self.price * tax_rate
        return self.price + tax_amount
    
    def __str__(self):
        return self.name

class PaymentQRCode(models.Model):
    name = models.CharField(max_length=100)
    qr_image = models.ImageField(upload_to='payment_qr/')
    is_active = models.BooleanField(default=True)
    created_at = models.DateTimeField(auto_now_add=True)

    def __str__(self):
        return self.name

class Sale(models.Model):
    id = models.AutoField(primary_key=True)
    cashier = models.ForeignKey(User, on_delete=models.SET_NULL, null=True, blank=True)
    subtotal = models.DecimalField(max_digits=10, decimal_places=2)
    tax_amount = models.DecimalField(max_digits=10, decimal_places=2)
    total_amount = models.DecimalField(max_digits=10, decimal_places=2)
    payment_qr = models.ForeignKey(PaymentQRCode, on_delete=models.SET_NULL, null=True)
    payment_status = models.BooleanField(default=False)
    payment_reference = models.CharField(max_length=100)
    created_at = models.DateTimeField(auto_now_add=True)    

    def calculate_totals(self):
        original_total = sum(item.original_total for item in self.items.all())
        self.tax_amount = original_total * Decimal('0.12')
        self.subtotal = original_total - self.tax_amount
        self.total_amount = self.subtotal + self.tax_amount
        self.save()
    
    def __str__(self):
        return f"Sale {self.id} - {self.created_at.strftime('%Y-%m-%d %H:%M')}"

class SaleItem(models.Model):
    sale = models.ForeignKey(Sale, on_delete=models.CASCADE, related_name='items')
    product = models.ForeignKey(Product, on_delete=models.CASCADE)
    quantity = models.IntegerField()
    price_at_sale = models.DecimalField(max_digits=10, decimal_places=2)
    tax_amount = models.DecimalField(max_digits=10, decimal_places=2)
    
    def save(self, *args, **kwargs):
        self.price_at_sale = self.product.price
        original_total = self.price_at_sale * self.quantity
        self.tax_amount = original_total * Decimal('0.12')
        super().save(*args, **kwargs)
        self.sale.calculate_totals()
    
    @property
    def original_total(self):
        return self.quantity * self.price_at_sale
    
    @property
    def subtotal(self):
        return self.original_total - self.tax_amount
    
    @property
    def total_with_tax(self):
        return self.subtotal + self.tax_amount
    
    def __str__(self):
        return f"{self.product.name} - {self.quantity}"