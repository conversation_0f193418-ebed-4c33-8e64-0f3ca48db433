"""
Django management command to set up a complete demo POS system.
This command will create an owner account, populate categories, products, and sample sales.
Usage: python manage.py setup_demo
"""

from django.core.management.base import BaseCommand, CommandError
from django.core.management import call_command
from django.contrib.auth.models import User
from django.db import transaction
import sys


class Command(BaseCommand):
    help = 'Set up a complete demo POS system with owner account, categories, products, and sample sales'

    def add_arguments(self, parser):
        parser.add_argument(
            '--skip-owner',
            action='store_true',
            help='Skip owner account creation',
        )
        parser.add_argument(
            '--skip-data',
            action='store_true',
            help='Skip data population (categories and products)',
        )
        parser.add_argument(
            '--skip-sales',
            action='store_true',
            help='Skip sample sales creation',
        )
        parser.add_argument(
            '--sales-count',
            type=int,
            default=100,
            help='Number of sample sales to create (default: 100)',
        )
        parser.add_argument(
            '--clear-all',
            action='store_true',
            help='Clear all existing data before setup',
        )

    def handle(self, *args, **options):
        self.stdout.write(
            self.style.SUCCESS('🚀 Setting up POS Demo System...\n')
        )

        try:
            with transaction.atomic():
                # Step 1: Create owner account
                if not options['skip_owner']:
                    self.create_owner_account()

                # Step 2: Populate categories and products
                if not options['skip_data']:
                    self.populate_data(options['clear_all'])

                # Step 3: Create sample sales
                if not options['skip_sales']:
                    self.create_sample_sales(options['sales_count'], options['clear_all'])

                self.display_final_summary()
                
        except Exception as e:
            self.stdout.write(
                self.style.ERROR(f'❌ Setup failed: {str(e)}')
            )
            raise CommandError(f'Demo setup failed: {str(e)}')

    def create_owner_account(self):
        """Create or verify owner account exists"""
        self.stdout.write('👤 Setting up owner account...')
        
        # Check if superuser already exists
        if User.objects.filter(is_superuser=True).exists():
            existing_user = User.objects.filter(is_superuser=True).first()
            self.stdout.write(
                self.style.WARNING(f'   ⚠ Superuser already exists: {existing_user.username}')
            )
            return

        # Create default demo owner
        try:
            owner = User.objects.create_superuser(
                username='demo_owner',
                email='<EMAIL>',
                password='demo123',
                first_name='Demo',
                last_name='Owner'
            )
            
            self.stdout.write(
                self.style.SUCCESS('   ✓ Created demo owner account')
            )
            self.stdout.write('     Username: demo_owner')
            self.stdout.write('     Password: demo123')
            self.stdout.write('     Email: <EMAIL>')
            
        except Exception as e:
            self.stdout.write(
                self.style.ERROR(f'   ❌ Failed to create owner: {str(e)}')
            )
            raise

    def populate_data(self, clear_existing):
        """Populate categories and products"""
        self.stdout.write('\n📦 Populating categories and products...')
        
        try:
            # Call the populate_data command
            call_command(
                'populate_data',
                clear_existing=clear_existing,
                verbosity=0  # Reduce verbosity to avoid duplicate output
            )
            
            self.stdout.write(
                self.style.SUCCESS('   ✓ Categories and products populated successfully')
            )
            
        except Exception as e:
            self.stdout.write(
                self.style.ERROR(f'   ❌ Failed to populate data: {str(e)}')
            )
            raise

    def create_sample_sales(self, sales_count, clear_existing):
        """Create sample sales data"""
        self.stdout.write(f'\n💰 Creating {sales_count} sample sales...')
        
        try:
            # Call the populate_sales command
            call_command(
                'populate_sales',
                count=sales_count,
                days=60,  # 60 days of sales history
                clear_existing=clear_existing,
                verbosity=0  # Reduce verbosity
            )
            
            self.stdout.write(
                self.style.SUCCESS(f'   ✓ Created {sales_count} sample sales')
            )
            
        except Exception as e:
            self.stdout.write(
                self.style.ERROR(f'   ❌ Failed to create sales: {str(e)}')
            )
            raise

    def display_final_summary(self):
        """Display final setup summary and instructions"""
        from pos.models import Category, Product, Sale
        from django.db import models
        
        # Get counts
        category_count = Category.objects.count()
        product_count = Product.objects.count()
        sales_count = Sale.objects.count()
        user_count = User.objects.count()
        
        # Calculate total revenue
        total_revenue = Sale.objects.aggregate(
            total=models.Sum('total_amount')
        )['total'] or 0

        self.stdout.write('\n' + '='*60)
        self.stdout.write(self.style.SUCCESS('🎉 POS DEMO SYSTEM SETUP COMPLETE! 🎉'))
        self.stdout.write('='*60)
        
        self.stdout.write('\n📊 System Summary:')
        self.stdout.write(f'   👥 Users: {user_count}')
        self.stdout.write(f'   📁 Categories: {category_count}')
        self.stdout.write(f'   🛍️ Products: {product_count}')
        self.stdout.write(f'   💰 Sales: {sales_count}')
        self.stdout.write(f'   💵 Total Revenue: ₱{total_revenue:,.2f}')

        self.stdout.write('\n🔐 Login Credentials:')
        superuser = User.objects.filter(is_superuser=True).first()
        if superuser:
            if superuser.username == 'demo_owner':
                self.stdout.write('   Username: demo_owner')
                self.stdout.write('   Password: demo123')
            else:
                self.stdout.write(f'   Username: {superuser.username}')
                self.stdout.write('   Password: [your existing password]')

        self.stdout.write('\n🌐 Access URLs:')
        self.stdout.write('   🏠 Homepage: http://localhost:8000/')
        self.stdout.write('   🔑 Login: http://localhost:8000/login/')
        self.stdout.write('   📊 Dashboard: http://localhost:8000/dashboard/')
        self.stdout.write('   🛒 Shopping: http://localhost:8000/api/create/')
        self.stdout.write('   📦 Products: http://localhost:8000/products/')
        self.stdout.write('   📁 Categories: http://localhost:8000/categories/')
        self.stdout.write('   💰 Sales: http://localhost:8000/sales/')

        self.stdout.write('\n🚀 Quick Start:')
        self.stdout.write('   1. Start server: python manage.py runserver')
        self.stdout.write('   2. Visit: http://localhost:8000/')
        self.stdout.write('   3. Click "Owner Login" and use credentials above')
        self.stdout.write('   4. Explore the dashboard and management features')
        self.stdout.write('   5. Try the shopping interface for customers')

        self.stdout.write('\n💡 Demo Features:')
        self.stdout.write('   ✅ Complete product catalog with realistic prices')
        self.stdout.write('   ✅ Multiple product categories')
        self.stdout.write('   ✅ Sample sales history with analytics')
        self.stdout.write('   ✅ Cash payment system with change calculation')
        self.stdout.write('   ✅ Receipt printing functionality')
        self.stdout.write('   ✅ Inventory management')
        self.stdout.write('   ✅ Sales reporting and analytics')

        self.stdout.write('\n🔧 Additional Commands:')
        self.stdout.write('   • python manage.py populate_data --clear-existing')
        self.stdout.write('   • python manage.py populate_sales --count 50')
        self.stdout.write('   • python manage.py createsuperuser (create additional users)')

        self.stdout.write('\n' + '='*60)
        self.stdout.write(self.style.SUCCESS('Ready to use! Enjoy your POS demo system! 🎊'))
        self.stdout.write('='*60)
