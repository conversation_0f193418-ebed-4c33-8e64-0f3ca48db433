@echo off
REM Django POS System Desktop Launcher
REM Double-click this file to start the POS system

title Django POS System

REM Change to the script directory
cd /d "%~dp0"

REM Hide the console window after starting
if not DEFINED IS_MINIMIZED set IS_MINIMIZED=1 && start "" /min "%~dpnx0" %* && exit

echo.
echo ========================================
echo    Django POS System Starting...
echo ========================================
echo.

REM Check if Python is available
python --version >nul 2>&1
if errorlevel 1 (
    echo ERROR: Python is not installed or not in PATH
    echo Please install Python and try again.
    pause
    exit /b 1
)

REM Start the desktop application
echo Starting POS system...
python desktop_launcher.py

REM If we get here, the application has stopped
echo.
echo POS system has stopped.
echo You can close this window.
pause
