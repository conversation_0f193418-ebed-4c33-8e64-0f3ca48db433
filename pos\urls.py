from django.urls import path
from pos import views
from django.conf import settings
from django.conf.urls.static import static

urlpatterns = [
    # Main pages
    path('', views.landing_page, name='landing'),
    path('login/', views.login_view, name='login'),
    path('dashboard/', views.dashboard, name='dashboard'),
    path('logout/', views.logout_view, name='logout'),

    # Products URLs
    path('products/', views.product_list, name='products'),
    path('products/add/', views.add_product, name='add_product'),
    path('products/edit/<int:product_id>/', views.edit_product, name='edit_product'),
    path('products/delete/<int:product_id>/', views.delete_product, name='delete_product'),
    path('products/delete-all/', views.delete_all_products, name='delete_all_products'),

    # Categories URLs
    path('categories/', views.category_list, name='categories'),
    path('categories/add/', views.add_category, name='add_category'),
    path('categories/edit/', views.edit_category, name='edit_category'),
    path('categories/delete/<int:category_id>/', views.delete_category, name='delete_category'),
    path('categories/delete-all/', views.delete_all_categories, name='delete_all_categories'),

    # Sales URLs
    path('sales/', views.sales_list, name='sales'),
    path('sales/<int:sale_id>/', views.sale_details, name='sale_details'),
    path('sales/report/', views.sales_report, name='sales_report'),
    path('sales/report/data/', views.sales_report_data, name='sales_report_data'),
    path('sales/delete-all/', views.delete_all_sales, name='delete_all_sales'),

    # QR Codes
    path('qr-codes/', views.qr_codes, name='qr_codes'),
    path('api/products/search/', views.search_products, name='search_products'),
    path('api/products/load-more/', views.load_more_products, name='load_more_products'),
    path('api/categories/', views.get_categories, name='get_categories'),

    # API endpoints
    path('api/create/', views.create_sale, name='create_sale'),
    path('api/create-sale/', views.create_sale_api, name='create_sale_api'),
    path('api/receipt/<int:sale_id>/', views.get_receipt_data, name='get_receipt_data'),
    path('api/sales/<int:sale_id>/delete/', views.delete_sale, name='delete_sale'),
    path('api/sales/bulk-delete/', views.bulk_delete_sales, name='bulk_delete_sales'),

    # Backup endpoints
    path('backup/', views.backup_dashboard, name='backup_dashboard'),
    path('api/backup/create/', views.create_backup_api, name='create_backup_api'),
    path('api/backup/status/', views.backup_status_api, name='backup_status_api'),
] + static(settings.MEDIA_URL, document_root=settings.MEDIA_ROOT)


