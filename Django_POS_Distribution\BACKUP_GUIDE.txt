DJANGO POS SYSTEM - BA<PERSON><PERSON>UP GUIDE
=================================

OVERVIEW:
--------
The POS system includes a comprehensive backup system to protect your data
from system failures, corruption, or accidental deletion.

BACKUP FEATURES:
---------------
✓ Automated daily backups
✓ Manual backup creation
✓ Compressed backup files
✓ 30-day retention policy
✓ Complete data protection
✓ Easy restore process

WHAT GETS BACKED UP:
-------------------
1. Database Data (JSON format)
   - All product information
   - Categories and inventory
   - Sales records and receipts
   - User accounts and settings

2. Database File (SQLite)
   - Complete database backup
   - Ready for immediate restore

3. Media Files
   - Product images
   - Uploaded files

4. Static Files
   - CSS, JavaScript, images
   - System assets

ACCESSING BACKUP SYSTEM:
-----------------------
1. Login to POS system
2. Click "Data Backup" in navigation menu
3. View backup dashboard

MANUAL BACKUP:
-------------
1. Go to Data Backup page
2. Click "Create Backup Now"
3. Wait for completion
4. Backup saved to backups/ folder

AUTOMATED DAILY BACKUP:
----------------------
To setup automatic daily backups:

1. Open command prompt in POS folder
2. Run: python manage.py schedule_daily_backup
3. Follow the instructions for your operating system

Windows:
- Creates Windows Task Scheduler entry
- Runs daily at 2:00 AM
- Requires administrator privileges

Linux/macOS:
- Creates cron job entry
- Runs daily at 2:00 AM
- Add to crontab manually

BACKUP COMMANDS:
---------------
Manual backup:
python manage.py backup_database

Compressed backup:
python manage.py backup_database --compress

Custom backup location:
python manage.py backup_database --backup-dir custom_backups

Keep backups for 60 days:
python manage.py backup_database --keep-days 60

Schedule daily backup:
python manage.py schedule_daily_backup

Schedule backup at specific time:
python manage.py schedule_daily_backup --time 03:30

Disable scheduled backup:
python manage.py schedule_daily_backup --disable

BACKUP LOCATIONS:
----------------
Default: backups/ folder in POS directory
Format: pos_backup_YYYYMMDD_HHMMSS/
Compressed: pos_backup_YYYYMMDD_HHMMSS.zip

BACKUP RETENTION:
----------------
- Keeps 30 days of backups by default
- Automatically deletes older backups
- Configurable retention period
- Compressed files save space

RESTORING FROM BACKUP:
---------------------
1. Stop the POS system
2. Locate backup file/folder
3. Extract if compressed
4. Copy database file to replace current db.sqlite3
5. Copy media files to media/ folder
6. Restart POS system

EMERGENCY RESTORE:
-----------------
If system fails completely:
1. Reinstall POS system
2. Run initial setup
3. Stop the system
4. Replace db.sqlite3 with backup
5. Copy media files from backup
6. Restart system

BACKUP VERIFICATION:
-------------------
1. Check backup dashboard regularly
2. Verify backup files exist
3. Test restore process periodically
4. Monitor backup sizes

TROUBLESHOOTING:
---------------
Problem: Backup fails
Solution: Check disk space and permissions

Problem: Scheduled backup not running
Solution: Verify task scheduler/cron setup

Problem: Large backup files
Solution: Clean old data or increase retention

Problem: Backup folder missing
Solution: Check backup directory path

BEST PRACTICES:
--------------
1. Test backups regularly
2. Keep multiple backup copies
3. Store backups off-site
4. Monitor backup status
5. Document restore procedures

BACKUP SECURITY:
---------------
- Backups contain sensitive data
- Store in secure location
- Encrypt if storing remotely
- Limit access to backup files
- Regular security audits

SUPPORT:
-------
For backup issues:
1. Check backup logs
2. Verify system permissions
3. Test manual backup
4. Contact system administrator

Remember: Backups are only useful if they work!
Test your backup and restore process regularly.
