    // Cart state management
    let cart = [];
    const TAX_RATE = 0.12;

    // Performance optimization variables
    let currentPage = 1;
    let isLoading = false;
    let hasMoreProducts = true;
    let searchTimeout = null;
    // CSRF Token handling
    function getCookie(name) {
        let cookieValue = null;
        if (document.cookie && document.cookie !== '') {
            const cookies = document.cookie.split(';');
            for (let i = 0; i < cookies.length; i++) {
                const cookie = cookies[i].trim();
                if (cookie.substring(0, name.length + 1) === (name + '=')) {
                    cookieValue = decodeURIComponent(cookie.substring(name.length + 1));
                    break;
                }
            }
        }
        return cookieValue;
    }

    // DOM Elements
    document.addEventListener('DOMContentLoaded', () => {
        const barcodeInput = document.getElementById('barcodeInput');
        const productSearch = document.getElementById('productSearch');
        const qrSelect = document.getElementById('qrCode');

        // Barcode scanner functionality
        barcodeInput.addEventListener('keypress', (e) => {
            if (e.key === 'Enter') {
                searchProductByBarcode(e.target.value);
                e.target.value = '';
            }
        });

        // Product search functionality
        productSearch.addEventListener('input', (e) => {
            filterProducts(e.target.value);
        });

        // QR code selection handling
        qrSelect.addEventListener('change', () => {
            const selectedOption = qrSelect.options[qrSelect.selectedIndex];
            if (selectedOption.dataset.image) {
                document.getElementById('selectedQRCode').src = selectedOption.dataset.image;
                document.getElementById('selectedQRName').textContent = selectedOption.text;
            }
        });
    });

        // Product handling functions
        const productStockMap = new Map(); // Stores original stock quantities

        function addProduct(id, name, price, stockQuantity) {
            // Initialize original stock if not already stored
            if (!productStockMap.has(id)) {
                productStockMap.set(id, parseInt(stockQuantity));
            }
            
            const originalStock = productStockMap.get(id);
            const existingItem = cart.find(item => item.id === id);
            
            if (existingItem) {
                if (existingItem.quantity + 1 > originalStock) {
                    showStockAlert(name, originalStock - existingItem.quantity);
                    return;
                }
                existingItem.quantity += 1;
                updateRealTimeStock(id, originalStock - existingItem.quantity);
            } else {
                if (originalStock <= 0) {
                    showStockAlert(name, 0);
                    return;
                }
                cart.push({
                    id: id,
                    name: name.toUpperCase(),
                    price: parseFloat(price),
                    quantity: 1,
                    maxStock: originalStock
                });
                updateRealTimeStock(id, originalStock - 1);
            }
            
            updateCartDisplay();
        }

        function updateRealTimeStock(productId, newStock) {
            const productCard = document.querySelector(`[data-id="${productId}"]`);
            if (productCard) {
                // Update stock display with animation
                const stockDisplay = productCard.querySelector('.inline-flex');
                stockDisplay.classList.add('scale-110', 'bg-yellow-100');
                
                setTimeout(() => {
                    stockDisplay.innerHTML = `
                        <i class="fas fa-cubes mr-2"></i>
                        Stock: ${newStock}
                    `;
                    
                    // Update stock indicator styling
                    stockDisplay.className = `inline-flex items-center px-4 py-2 text-sm font-medium rounded-full transition-all duration-300 ${
                        newStock <= 10 ? 'bg-red-100 text-red-800 border border-red-200' : 'bg-green-100 text-green-800 border border-green-200'
                    }`;
                }, 150);

                setTimeout(() => {
                    stockDisplay.classList.remove('scale-110', 'bg-yellow-100');
                }, 300);

                // Update add to cart button state
                const addButton = productCard.querySelector('button');
                if (newStock <= 0) {
                    addButton.innerHTML = '<i class="fas fa-cart-plus mr-2"></i>Out of Stock';
                    addButton.classList.add('opacity-50', 'cursor-not-allowed');
                    addButton.disabled = true;
                    addButton.classList.remove('hover:bg-indigo-700');
                    addButton.classList.add('bg-gray-400');
                } else {
                    addButton.innerHTML = '<i class="fas fa-cart-plus mr-2"></i>Add to Cart';
                    addButton.classList.remove('opacity-50', 'cursor-not-allowed', 'bg-gray-400');
                    addButton.classList.add('hover:bg-indigo-700');
                    addButton.disabled = false;
                }
            }
        }

        function showStockAlert(productName, availableStock) {
            const alertDiv = document.createElement('div');
            alertDiv.className = 'fixed top-4 right-4 bg-red-100 border-l-4 border-red-500 text-red-700 p-4 rounded shadow-lg';
            alertDiv.innerHTML = `
                <div class="flex items-center">
                    <i class="fas fa-exclamation-circle mr-2"></i>
                    <div>
                        <p class="font-bold">Insufficient Stock</p>
                        <p>${productName} has only ${availableStock} units available.</p>
                    </div>
                </div>
            `;
            document.body.appendChild(alertDiv);
            setTimeout(() => alertDiv.remove(), 3000);
        }

        function removeProduct(index) {
            const item = cart[index];
            const originalStock = productStockMap.get(item.id);
            const newStock = originalStock - (cart.reduce((total, cartItem) => 
                cartItem.id === item.id ? total + cartItem.quantity : total, 0) - item.quantity);
            
            // Ensure stock doesn't exceed original amount
            const finalStock = Math.min(newStock, originalStock);
            
            updateRealTimeStock(item.id, finalStock);
            cart.splice(index, 1);
            updateCartDisplay();
        }

        function updateQuantity(index, newQuantity) {
            const item = cart[index];
            const parsedQuantity = parseInt(newQuantity);
            const originalStock = productStockMap.get(item.id);
            
            // Calculate total quantity of this item in cart excluding current item
            const otherQuantities = cart.reduce((total, cartItem, idx) => 
                cartItem.id === item.id && idx !== index ? total + cartItem.quantity : total, 0);
            
            // Check if new quantity would exceed original stock
            if (parsedQuantity + otherQuantities > originalStock) {
                showStockAlert(item.name, originalStock - otherQuantities);
                return;
            }

            if (parsedQuantity <= 0) {
                updateRealTimeStock(item.id, originalStock - otherQuantities);
                cart.splice(index, 1);
            } else {
                item.quantity = parsedQuantity;
                updateRealTimeStock(item.id, originalStock - (parsedQuantity + otherQuantities));
            }

            updateCartDisplay();
        }
        function updateProductCardStock(productId, newStock) {
            const productCard = document.querySelector(`[data-id="${productId}"]`);
            if (productCard) {
                const stockElement = productCard.querySelector('.stock-quantity');
                if (stockElement) {
                    stockElement.textContent = `Stock: ${newStock}`;
            
                    // Update visual indicators based on stock level
                    const stockIndicator = productCard.querySelector('.stock-indicator');
                    if (stockIndicator) {
                        stockIndicator.className = `inline-flex items-center px-3 py-1 text-sm rounded-full ${
                            newStock <= 10 ? 'bg-red-100 text-red-800' : 'bg-green-100 text-green-800'
                        }`;
                    }
                }
            }
        }
    // Cart display functions
    function updateCartDisplay() {
        const cartItems = document.getElementById('cartItems');
        cartItems.innerHTML = '';
    
        cart.forEach((item, index) => {
            const itemTotal = item.price * item.quantity;
            const originalStock = productStockMap.get(item.id);
            const maxAllowedQuantity = originalStock;
            
            cartItems.innerHTML += `
                <div class="flex justify-between items-center p-2 border-b">
                    <div class="flex-1">
                        <p class="font-medium">${item.name}</p>
                        <p class="text-gray-600">₱${formatNumber(item.price.toFixed(2))}  
                            <input type="number" 
                                value="${item.quantity}" 
                                min="1" 
                                max="${maxAllowedQuantity}"
                                class="w-16 border rounded px-1 ${item.quantity >= maxAllowedQuantity ? 'bg-red-50 border-red-300' : ''}"
                                onchange="updateQuantity(${index}, this.value)"
                                ${item.quantity >= maxAllowedQuantity ? 'disabled' : ''}>
                            <span class="text-xs text-gray-500">
                                (Max: ${maxAllowedQuantity})
                            </span>
                        </p>
                    </div>
                    <div class="text-right">
                        <p class="font-medium">₱${formatNumber(itemTotal.toFixed(2))}</p>
                        <button onclick="removeProduct(${index})" 
                            class="text-red-500 hover:text-red-700">Remove</button>
                    </div>
                </div>
            `;
        });
    
        updateTotals();
    }
    function updateTotals() {
        const originalTotal = cart.reduce((sum, item) => sum + (item.price * item.quantity), 0);
        const tax = originalTotal * TAX_RATE;
        const subtotal = originalTotal - tax;
        const total = subtotal + tax; // This will equal the originalTotal

        document.getElementById('subtotal').textContent = `₱${formatNumber(subtotal.toFixed(2))}`;
        document.getElementById('tax').textContent = `₱${formatNumber(tax.toFixed(2))}`;
        document.getElementById('total').textContent = `₱${formatNumber(total.toFixed(2))}`;
    }

    function showPaymentModal() {
        if (cart.length === 0) {
            alert('Please add items to cart first');
            return;
        }

        const modal = document.getElementById('paymentQRModal');
        modal.classList.remove('hidden');

        const originalTotal = cart.reduce((sum, item) => sum + (item.price * item.quantity), 0);
        const tax = originalTotal * TAX_RATE;
        const subtotal = originalTotal - tax;
        const total = subtotal + tax;

        // Store total for calculations
        window.currentTotal = total;

        document.getElementById('modalSubtotal').textContent = `₱${formatNumber(subtotal.toFixed(2))}`;
        document.getElementById('modalTax').textContent = `₱${formatNumber(tax.toFixed(2))}`;
        document.getElementById('modalTotal').textContent = `₱${formatNumber(total.toFixed(2))}`;

        const orderItems = document.getElementById('orderItems');
        orderItems.innerHTML = cart.map(item => `
            <div class="flex justify-between items-center py-2 border-b border-gray-200">
                <div class="flex-1">
                    <span class="font-medium">${item.name}</span>
                    <span class="text-gray-500 ml-2">× ${item.quantity}</span>
                </div>
                <span class="font-medium">₱${formatNumber((item.price * item.quantity).toFixed(2))}</span>
            </div>
        `).join('');

        // Reset cash input and change calculation
        document.getElementById('cashReceived').value = '';
        document.getElementById('changeAmount').textContent = '₱0.00';
        document.getElementById('changeStatus').textContent = '';
        document.getElementById('saveAndPrintBtn').disabled = true;

        // Focus on cash input
        setTimeout(() => {
            document.getElementById('cashReceived').focus();
        }, 100);
    }
    function closePaymentModal() {
        document.getElementById('paymentQRModal').classList.add('hidden');
        // Reset all cash payment fields
        document.getElementById('cashReceived').value = '';
        document.getElementById('changeAmount').textContent = '₱0.00';
        document.getElementById('changeStatus').textContent = '';
        document.getElementById('saveAndPrintBtn').disabled = true;
    }

    // Cash Calculator Functions
    function calculateChange() {
        const cashReceived = parseFloat(document.getElementById('cashReceived').value) || 0;
        const total = window.currentTotal || 0;
        const change = cashReceived - total;

        const changeAmountElement = document.getElementById('changeAmount');
        const changeStatusElement = document.getElementById('changeStatus');
        const saveBtn = document.getElementById('saveAndPrintBtn');

        if (cashReceived === 0) {
            changeAmountElement.textContent = '₱0.00';
            changeStatusElement.textContent = '';
            saveBtn.disabled = true;
            changeAmountElement.className = 'text-3xl font-bold text-yellow-800';
        } else if (change < 0) {
            changeAmountElement.textContent = `₱${formatNumber(Math.abs(change).toFixed(2))}`;
            changeStatusElement.textContent = `Insufficient payment. Need ₱${formatNumber(Math.abs(change).toFixed(2))} more.`;
            saveBtn.disabled = true;
            changeAmountElement.className = 'text-3xl font-bold text-red-600';
        } else {
            changeAmountElement.textContent = `₱${formatNumber(change.toFixed(2))}`;
            changeStatusElement.textContent = change === 0 ? 'Exact amount received.' : 'Payment sufficient.';
            saveBtn.disabled = false;
            changeAmountElement.className = 'text-3xl font-bold text-green-600';
        }
    }

    function setQuickAmount(amount) {
        document.getElementById('cashReceived').value = amount.toFixed(2);
        calculateChange();
    }

    function setExactAmount() {
        const total = window.currentTotal || 0;
        document.getElementById('cashReceived').value = total.toFixed(2);
        calculateChange();
    }

    function clearCashAmount() {
        document.getElementById('cashReceived').value = '';
        calculateChange();
    }

    // Reset Cart Function
    function resetCart() {
        if (cart.length === 0) {
            showNotification('Cart is already empty', 'info');
            return;
        }

        // Show confirmation dialog
        if (confirm('Are you sure you want to clear all items from the cart?')) {
            cart = [];
            updateCartDisplay();
            showNotification('Cart has been reset successfully', 'success');
        }
    }

    // Notification function for better user feedback
    function showNotification(message, type = 'info') {
        const notification = document.createElement('div');
        notification.className = `fixed top-4 right-4 p-4 rounded-lg shadow-lg z-50 transition-all duration-300 transform translate-x-full`;

        // Set colors based on type
        const colors = {
            success: 'bg-green-100 border-l-4 border-green-500 text-green-700',
            error: 'bg-red-100 border-l-4 border-red-500 text-red-700',
            warning: 'bg-yellow-100 border-l-4 border-yellow-500 text-yellow-700',
            info: 'bg-blue-100 border-l-4 border-blue-500 text-blue-700'
        };

        const icons = {
            success: 'fas fa-check-circle',
            error: 'fas fa-exclamation-triangle',
            warning: 'fas fa-exclamation-circle',
            info: 'fas fa-info-circle'
        };

        notification.className += ` ${colors[type] || colors.info}`;
        notification.innerHTML = `
            <div class="flex items-center">
                <i class="${icons[type] || icons.info} mr-2"></i>
                <div>
                    <p class="font-medium">${message}</p>
                </div>
                <button onclick="this.parentElement.parentElement.remove()" class="ml-4 hover:opacity-70">
                    <i class="fas fa-times"></i>
                </button>
            </div>
        `;

        document.body.appendChild(notification);

        // Animate in
        setTimeout(() => {
            notification.classList.remove('translate-x-full');
        }, 100);

        // Auto remove after 3 seconds
        setTimeout(() => {
            if (notification.parentNode) {
                notification.classList.add('translate-x-full');
                setTimeout(() => {
                    if (notification.parentNode) {
                        notification.remove();
                    }
                }, 300);
            }
        }, 3000);
    }
      function saveAndPrint() {
          const cashReceived = parseFloat(document.getElementById('cashReceived').value) || 0;
          const total = window.currentTotal || 0;

          if (cashReceived < total) {
              alert('Insufficient cash received. Please enter the correct amount.');
              return;
          }

          const originalTotal = cart.reduce((sum, item) => sum + (item.price * item.quantity), 0);
          const tax = originalTotal * TAX_RATE;
          const subtotal = originalTotal - tax;
          const change = cashReceived - total;

          const saleData = {
              items: cart.map(item => ({
                  id: item.id,
                  quantity: item.quantity,
                  price: item.price
              })),
              payment_reference: `CASH-${Date.now()}`, // Generate cash payment reference
              subtotal: subtotal,
              tax_amount: tax,
              total_amount: total,
              cash_received: cashReceived,
              change_amount: change
          };

          // Disable button to prevent double submission
          const saveBtn = document.getElementById('saveAndPrintBtn');
          saveBtn.disabled = true;
          saveBtn.innerHTML = '<i class="fas fa-spinner fa-spin mr-2"></i>Processing...';

          fetch('/api/create-sale/', {
              method: 'POST',
              headers: {
                  'Content-Type': 'application/json',
                  'X-CSRFToken': getCookie('csrftoken')
              },
              body: JSON.stringify(saleData)
          })
          .then(response => response.json())
          .then(data => {
              if (data.success) {
                  generateCashReceipt(cashReceived, change);
                  setTimeout(() => {
                      cart = [];
                      updateCartDisplay();
                      closePaymentModal();
                      window.location.href = '/api/create/';
                  }, 1000);
              } else {
                  alert('Error creating sale: ' + (data.error || 'Unknown error'));
                  saveBtn.disabled = false;
                  saveBtn.innerHTML = '<i class="fas fa-save mr-2"></i>Save & Print';
              }
          })
          .catch(error => {
              console.error('Error:', error);
              alert('An error occurred while processing the sale');
              saveBtn.disabled = false;
              saveBtn.innerHTML = '<i class="fas fa-save mr-2"></i>Save & Print';
          });
      }
    function generateCashReceipt(cashReceived, change) {
        const receipt = document.getElementById('receiptTemplate').cloneNode(true);
        receipt.id = '';
        receipt.classList.remove('hidden');

        const now = new Date();
        receipt.querySelector('#receiptDate').textContent = now.toLocaleDateString();
        receipt.querySelector('#receiptTime').textContent = now.toLocaleTimeString();
        receipt.querySelector('#receiptNumber').textContent = generateReceiptNumber();
        receipt.querySelector('#receiptReference').textContent = `CASH-${Date.now()}`;

        const originalTotal = cart.reduce((sum, item) => sum + (item.price * item.quantity), 0);
        const tax = originalTotal * TAX_RATE;
        const subtotal = originalTotal - tax;
        const total = subtotal + tax;

        receipt.querySelector('#receiptItems').innerHTML = cart.map(item => `
            <div style="display: flex; justify-content: space-between;">
                <span>${item.name} × ${item.quantity}</span>
                <span>₱${formatNumber((item.price * item.quantity).toFixed(2))}</span>
            </div>
        `).join('');

        receipt.querySelector('#receiptSubtotal').textContent = `₱${formatNumber(subtotal.toFixed(2))}`;
        receipt.querySelector('#receiptTax').textContent = `₱${formatNumber(tax.toFixed(2))}`;
        receipt.querySelector('#receiptTotal').textContent = `₱${formatNumber(total.toFixed(2))}`;

        // Add cash payment details to receipt
        const receiptTotalsDiv = receipt.querySelector('#receiptTotal').parentElement.parentElement;
        receiptTotalsDiv.innerHTML += `
            <div style="border-top: 1px dashed #000; padding-top: 2mm; margin-top: 2mm;">
                <div style="display: flex; justify-content: space-between; margin: 1mm 0; font-size: 8pt;">
                    <span>Cash Received:</span>
                    <span>₱${formatNumber(cashReceived.toFixed(2))}</span>
                </div>
                <div style="display: flex; justify-content: space-between; font-weight: bold; margin-top: 2mm; padding-top: 2mm; border-top: 1px solid #000; font-size: 10pt;">
                    <span>Change:</span>
                    <span>₱${formatNumber(change.toFixed(2))}</span>
                </div>
            </div>
        `;

        const printWindow = window.open('', 'Print Receipt', 'height=600,width=800');
        printWindow.document.write(receipt.outerHTML);
        printWindow.document.close();
        printWindow.print();
    }      function generateReceiptNumber() {
          return 'RCP' + Date.now().toString().slice(-8);
      }
        // Enhanced search functionality with multiple filters
        function initializeAdvancedSearch() {
            const searchInput = document.getElementById('productSearch');
            const barcodeInput = document.getElementById('barcodeInput');

            function updateProductGrid(products, append = false) {
                const productGrid = document.getElementById('productGrid');
                const loadingIndicator = document.getElementById('loadingIndicator');

                if (loadingIndicator) {
                    loadingIndicator.style.display = 'none';
                }

                const productHTML = products.map(product => `
                    <div class="card-hover bg-white rounded-lg p-4 border cursor-pointer transform hover:scale-105 transition-all duration-300 hover:shadow-lg"
                         data-id="${product.id}"
                         data-barcode="${product.barcode}"
                         data-category="${product.category}"
                         data-price="${product.price}">
                        <div class="flex items-center mb-2">
                            <i class="fas fa-box text-indigo-500 mr-2"></i>
                            <h3 class="font-semibold text-gray-800">${product.name}</h3>
                        </div>
                        <p class="text-indigo-600 font-bold text-xl mt-2 flex items-center">
                            <i class="fas fa-tag text-indigo-400 mr-2"></i>
                            ₱${formatNumber(product.price)}
                        </p>
                        <div class="mt-3">
                            <span class="inline-flex items-center px-3 py-1 text-sm rounded-full ${product.stock_quantity <= 10 ? 'bg-red-100 text-red-800' : 'bg-green-100 text-green-800'}">
                                <i class="fas fa-cubes mr-1"></i>
                                Stock: ${product.stock_quantity}
                            </span>
                        </div>
                        <button onclick="addProduct('${product.id}', '${product.name}', '${product.price}', '${product.stock_quantity}')"
                                class="w-full mt-3 bg-indigo-600 text-white py-2 rounded-lg font-semibold hover:bg-indigo-700 transition-colors flex items-center justify-center
                                       ${product.stock_quantity <= 0 ? 'opacity-50 cursor-not-allowed' : ''}"
                                ${product.stock_quantity <= 0 ? 'disabled' : ''}>
                            <i class="fas fa-cart-plus mr-2"></i>
                            ${product.stock_quantity <= 0 ? 'Out of Stock' : 'Add to Cart'}
                        </button>
                    </div>
                `).join('');

                if (append) {
                    productGrid.innerHTML += productHTML;
                } else {
                    productGrid.innerHTML = productHTML;
                }
            }

            function performSearch(resetPage = true) {
                if (isLoading) return;

                if (resetPage) {
                    currentPage = 1;
                    hasMoreProducts = true;
                }

                isLoading = true;
                showLoadingIndicator();

                const searchParams = new URLSearchParams({
                    search: searchInput.value,
                    barcode: barcodeInput.value,
                    category: document.getElementById('categoryFilter')?.value || '',
                    min_price: document.getElementById('minPrice')?.value || '',
                    max_price: document.getElementById('maxPrice')?.value || '',
                    page: currentPage,
                    limit: 20
                });

                fetch(`/api/products/search/?${searchParams}`)
                    .then(response => response.json())
                    .then(data => {
                        if (data.error) {
                            console.error('Server error:', data.error);
                            showNotification('Error loading products. Please try again.', 'error');
                        } else {
                            updateProductGrid(data.products, !resetPage);
                            hasMoreProducts = data.has_more;
                        }
                        isLoading = false;
                        updateLoadMoreButton();
                    })
                    .catch(error => {
                        console.error('Error searching products:', error);
                        showNotification('Network error. Please check your connection.', 'error');
                        isLoading = false;
                        hideLoadingIndicator();
                        updateLoadMoreButton();
                    });
            }

            function loadMoreProducts() {
                if (isLoading || !hasMoreProducts) return;

                currentPage++;
                performSearch(false);
            }

            function showLoadingIndicator() {
                let loadingIndicator = document.getElementById('loadingIndicator');
                if (!loadingIndicator) {
                    loadingIndicator = document.createElement('div');
                    loadingIndicator.id = 'loadingIndicator';
                    loadingIndicator.className = 'flex justify-center items-center p-4';
                    loadingIndicator.innerHTML = `
                        <div class="animate-spin rounded-full h-8 w-8 border-b-2 border-indigo-600"></div>
                        <span class="ml-2 text-gray-600">Loading products...</span>
                    `;
                    document.getElementById('productGrid').parentNode.appendChild(loadingIndicator);
                }
                loadingIndicator.style.display = 'flex';
            }

            function hideLoadingIndicator() {
                const loadingIndicator = document.getElementById('loadingIndicator');
                if (loadingIndicator) {
                    loadingIndicator.style.display = 'none';
                }
            }

            function updateLoadMoreButton() {
                const loadMoreBtn = document.getElementById('loadMoreBtn');
                if (loadMoreBtn) {
                    if (hasMoreProducts && !isLoading) {
                        loadMoreBtn.classList.remove('hidden');
                    } else {
                        loadMoreBtn.classList.add('hidden');
                    }
                }
            }



            // Load categories on page load
            fetch('/api/categories/')
                .then(response => response.json())
                .then(data => {
                    const categoryFilter = document.getElementById('categoryFilter');
                    if (categoryFilter) {
                        data.categories.forEach(category => {
                            const option = document.createElement('option');
                            option.value = category;
                            option.textContent = category;
                            categoryFilter.appendChild(option);
                        });
                    }
                });

            // Event listeners for all filter inputs with debouncing
            [searchInput, barcodeInput, 'categoryFilter', 'minPrice', 'maxPrice'].forEach(id => {
                const element = typeof id === 'string' ? document.getElementById(id) : id;
                if (element) {
                    element.addEventListener('input', () => {
                        clearTimeout(searchTimeout);
                        searchTimeout = setTimeout(() => performSearch(true), 300);
                    });
                }
            });

            // Infinite scrolling for product grid
            const productGridContainer = document.getElementById('productGrid');
            if (productGridContainer) {
                productGridContainer.addEventListener('scroll', () => {
                    const { scrollTop, scrollHeight, clientHeight } = productGridContainer;
                    if (scrollTop + clientHeight >= scrollHeight - 100) { // Load more when 100px from bottom
                        loadMoreProducts();
                    }
                });
            }

            // Initial load of products
            performSearch(true);
        }

        document.addEventListener('DOMContentLoaded', initializeAdvancedSearch);
          function searchProductByBarcode(barcode) {
              const productCard = document.querySelector(`[data-barcode="${barcode}"]`);
            
              if (productCard) {
                  // Extract product details from the card
                  const productId = productCard.getAttribute('data-id');
                  const productName = productCard.querySelector('h3').textContent;
                  const productPrice = productCard.getAttribute('data-price');
                  const stockQuantity = productCard.querySelector('span').textContent.match(/\d+/)[0];
                
                  // Add to cart automatically
                  addProduct(productId, productName, productPrice, stockQuantity);
                
                  // Visual feedback for scanned item
                  productCard.classList.add('bg-indigo-50', 'scale-105');
                  setTimeout(() => {
                      productCard.classList.remove('bg-indigo-50', 'scale-105');
                  }, 500);
                
                  // Clear and refocus barcode input
                  const barcodeInput = document.getElementById('barcodeInput');
                  barcodeInput.value = '';
                  barcodeInput.focus();
              }
          }

          // Update barcode input listener
          document.addEventListener('DOMContentLoaded', () => {
              const barcodeInput = document.getElementById('barcodeInput');
              barcodeInput.addEventListener('keypress', (e) => {
                  if (e.key === 'Enter') {
                      searchProductByBarcode(e.target.value);
                  }
              });
          });

          // Add this search functionality
          document.getElementById('productSearch').addEventListener('input', function(e) {
              const searchTerm = e.target.value.toLowerCase();
              const productCards = document.querySelectorAll('#productGrid > div');
            
              productCards.forEach(card => {
                  const productName = card.querySelector('h3').textContent.toLowerCase();
                  const productBarcode = card.dataset.barcode.toLowerCase();
                  const productCategory = card.dataset.category.toLowerCase();
                  const productPrice = card.dataset.price;
                
                  // Comprehensive search across multiple fields
                  const matches = 
                      productName.includes(searchTerm) ||
                      productBarcode.includes(searchTerm) ||
                      productCategory.includes(searchTerm) ||
                      productPrice.includes(searchTerm);
                
                  // Show/hide with smooth transition
                  if (matches) {
                      card.style.display = 'block';
                      card.style.opacity = '1';
                  } else {
                      card.style.opacity = '0';
                      setTimeout(() => {
                          card.style.display = 'none';
                      }, 300);
                  }
              });
          });

          // Add real-time barcode scanning
          document.getElementById('barcodeInput').addEventListener('input', function(e) {
              const barcode = e.target.value;
              const productCards = document.querySelectorAll('#productGrid > div');
            
              productCards.forEach(card => {
                  if (card.dataset.barcode === barcode) {
                      // Highlight matching product
                      card.classList.add('ring-2', 'ring-indigo-500', 'scale-105');
                      // Automatically scroll to the matching product
                      card.scrollIntoView({ behavior: 'smooth', block: 'center' });
                  } else {
                      card.classList.remove('ring-2', 'ring-indigo-500', 'scale-105');
                  }
              });
        });

function formatNumber(number) {
    return number.toString().replace(/\B(?=(\d{3})+(?!\d))/g, ",");
}
