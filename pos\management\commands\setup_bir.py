"""
Django management command to setup BIR compliance information
Usage: python manage.py setup_bir
"""

from django.core.management.base import BaseCommand
from django.conf import settings
import os
import json
from pathlib import Path

class Command(BaseCommand):
    help = 'Setup BIR compliance information for receipts'

    def add_arguments(self, parser):
        parser.add_argument(
            '--tin',
            type=str,
            help='Your business TIN number',
        )
        parser.add_argument(
            '--atp-number',
            type=str,
            help='Authority to Print (ATP) number',
        )
        parser.add_argument(
            '--atp-date',
            type=str,
            help='ATP date issued (YYYY-MM-DD)',
        )
        parser.add_argument(
            '--business-name',
            type=str,
            help='Your business name',
        )
        parser.add_argument(
            '--business-address',
            type=str,
            help='Your business address',
        )
        parser.add_argument(
            '--interactive',
            action='store_true',
            help='Run in interactive mode',
        )

    def handle(self, *args, **options):
        self.stdout.write(self.style.SUCCESS('BIR Compliance Setup'))
        self.stdout.write('=' * 30)
        
        if options['interactive']:
            self.interactive_setup()
        else:
            self.command_line_setup(options)
        
        self.stdout.write()
        self.stdout.write(self.style.SUCCESS('BIR setup completed!'))
        self.stdout.write('Remember to:')
        self.stdout.write('1. Verify all information is correct')
        self.stdout.write('2. Test print a receipt')
        self.stdout.write('3. Keep your ATP documents safe')

    def interactive_setup(self):
        """Interactive setup mode"""
        self.stdout.write()
        self.stdout.write('Interactive BIR Setup')
        self.stdout.write('-' * 20)
        
        # Collect information
        business_name = input('Business Name: ').strip()
        business_address = input('Business Address: ').strip()
        tin_number = input('TIN Number (e.g., 123-456-789-000): ').strip()
        atp_number = input('ATP Number: ').strip()
        atp_date = input('ATP Date Issued (YYYY-MM-DD): ').strip()
        atp_valid_until = input('ATP Valid Until (YYYY-MM-DD): ').strip()
        series_from = input('Receipt Series From (default: 0000001): ').strip() or '0000001'
        series_to = input('Receipt Series To (default: 9999999): ').strip() or '9999999'
        
        # Create BIR config
        bir_config = {
            'business_name': business_name,
            'business_address': business_address,
            'tin_number': tin_number,
            'atp_number': atp_number,
            'atp_date_issued': atp_date,
            'atp_valid_until': atp_valid_until,
            'receipt_series_from': series_from,
            'receipt_series_to': series_to,
        }
        
        self.save_config(bir_config)
        self.update_templates(bir_config)

    def command_line_setup(self, options):
        """Command line setup mode"""
        bir_config = {}
        
        if options['tin']:
            bir_config['tin_number'] = options['tin']
        if options['atp_number']:
            bir_config['atp_number'] = options['atp_number']
        if options['atp_date']:
            bir_config['atp_date_issued'] = options['atp_date']
        if options['business_name']:
            bir_config['business_name'] = options['business_name']
        if options['business_address']:
            bir_config['business_address'] = options['business_address']
        
        if bir_config:
            self.save_config(bir_config)
            self.update_templates(bir_config)
        else:
            self.stdout.write(self.style.WARNING('No BIR information provided.'))
            self.stdout.write('Use --interactive flag for guided setup.')

    def save_config(self, config):
        """Save BIR configuration to file"""
        config_file = Path(settings.BASE_DIR) / 'bir_config.json'
        
        # Load existing config if it exists
        existing_config = {}
        if config_file.exists():
            try:
                with open(config_file, 'r') as f:
                    existing_config = json.load(f)
            except:
                pass
        
        # Update with new config
        existing_config.update(config)
        
        # Save updated config
        with open(config_file, 'w') as f:
            json.dump(existing_config, f, indent=2)
        
        self.stdout.write(f'BIR configuration saved to: {config_file}')

    def update_templates(self, config):
        """Update receipt templates with BIR information"""
        # This would update the actual template files
        # For now, we'll just show what needs to be updated
        
        self.stdout.write()
        self.stdout.write('Template Update Required:')
        self.stdout.write('-' * 25)
        
        replacements = {
            '[Your TIN Number]': config.get('tin_number', '[Your TIN Number]'),
            '[ATP Number]': config.get('atp_number', '[ATP Number]'),
            '[ATP Date]': config.get('atp_date_issued', '[ATP Date]'),
        }
        
        for placeholder, value in replacements.items():
            self.stdout.write(f'Replace "{placeholder}" with "{value}"')

    def show_current_config(self):
        """Show current BIR configuration"""
        config_file = Path(settings.BASE_DIR) / 'bir_config.json'
        
        if config_file.exists():
            try:
                with open(config_file, 'r') as f:
                    config = json.load(f)
                
                self.stdout.write('Current BIR Configuration:')
                self.stdout.write('-' * 30)
                for key, value in config.items():
                    self.stdout.write(f'{key}: {value}')
            except:
                self.stdout.write('Error reading BIR configuration file.')
        else:
            self.stdout.write('No BIR configuration found.')
            self.stdout.write('Run: python manage.py setup_bir --interactive')
