{% extends 'base/base.html' %}
{% load humanize %}

{% block title %}Sale Details #{{ sale.id }} - POS System{% endblock %}

{% block content %}
    <div class="bg-white shadow overflow-hidden sm:rounded-lg">
    <!-- Header Section -->
        <div class="px-4 py-5 sm:px-6">
            <div class="flex justify-between items-center">
                <h1 class="text-2xl font-semibold text-gray-900">Sale Details #{{ sale.id }}</h1>
                <a href="{% url 'sales' %}" class="bg-gray-500 text-white px-4 py-2 rounded-md hover:bg-gray-600">
                    <i class="fas fa-arrow-left mr-2"></i>Back to Sales
                </a>
            </div>
        </div>

    <!-- Sale Information -->
        <div class="border-t border-gray-200 px-4 py-5 sm:px-6">
            <dl class="grid grid-cols-1 gap-x-4 gap-y-6 sm:grid-cols-3">
                <div class="sm:col-span-1">
                    <dt class="text-sm font-medium text-gray-500">Transaction Date</dt>
                    <dd class="mt-1 text-sm text-gray-900">
                        <i class="far fa-calendar-alt mr-1"></i>
                        {{ sale.created_at|date:"F d, Y H:i" }}
                    </dd>
                </div>
            {% comment %} <div class="sm:col-span-1">
                <dt class="text-sm font-medium text-gray-500">Cashier</dt>
                <dd class="mt-1 text-sm text-gray-900">
                    <i class="fas fa-user mr-1"></i>
                    {{ sale.cashier.username }}
                </dd>
            </div> {% endcomment %}
                <div class="sm:col-span-1">
                    <dt class="text-sm font-medium text-gray-500">Payment Status</dt>
                    <dd class="mt-1">
                        {% if sale.payment_status %}
                            <span class="px-2 inline-flex text-xs leading-5 font-semibold rounded-full bg-green-100 text-green-800">
                                <i class="fas fa-check-circle mr-1"></i>Paid
                            </span>
                        {% else %}
                            <span class="px-2 inline-flex text-xs leading-5 font-semibold rounded-full bg-red-100 text-red-800">
                                <i class="fas fa-clock mr-1"></i>Pending
                            </span>
                        {% endif %}
                    </dd>
                </div>
                {% if sale.payment_reference %}
                    <div class="sm:col-span-1">
                        <dt class="text-sm font-medium text-gray-500">Payment Reference</dt>
                        <dd class="mt-1 text-sm text-gray-900">
                            <i class="fas fa-hashtag mr-1"></i>
                            {{ sale.payment_reference }}
                        </dd>
                    </div>
                {% endif %}
                {% if sale.payment_qr %}
                    <div class="sm:col-span-1">
                        <dt class="text-sm font-medium text-gray-500">Payment QR</dt>
                        <dd class="mt-1 text-sm text-gray-900">
                            <img src="{{ sale.payment_qr.qr_image.url }}" alt="Payment QR" class="h-24 w-24">
                        </dd>
                    </div>
                {% endif %}
            </dl>
        </div>

    <!-- Items Table -->
        <div class="px-4 py-5 sm:px-6">
            <h2 class="text-lg font-medium text-gray-900 mb-4">Sale Items</h2>
            <div class="overflow-x-auto">
                <table class="min-w-full divide-y divide-gray-200">
                    <thead class="bg-gray-50">
                        <tr>
                            <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Product</th>
                            <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Quantity</th>
                            <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Unit Price</th>
                            <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Tax Amount</th>
                            <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Subtotal</th>
                            <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Total with Tax</th>
                        </tr>
                    </thead>
                    <tbody class="bg-white divide-y divide-gray-200">
                        {% for item in sale.items.all %}
                            <tr>
                                <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                                    <div class="flex items-center">
                                        <div>
                                            <div class="font-medium">{{ item.product.name|title }}</div>
                                            <div class="text-gray-500">{{ item.product.barcode }}</div>
                                        </div>
                                    </div>
                                </td>
                                <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">{{ item.quantity }}</td>
                                <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">₱{{ item.price_at_sale|intcomma }}</td>
                                <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">₱{{ item.tax_amount|intcomma }}</td>
                                <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">₱{{ item.subtotal|intcomma }}</td>
                                <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">₱{{ item.total_with_tax|intcomma }}</td>
                            </tr>
                        {% endfor %}
                    </tbody>
                </table>
            </div>
        </div>

    <!-- Totals Section -->
        <div class="border-t border-gray-200 px-4 py-5 sm:px-6">
            <dl class="grid grid-cols-1 gap-x-4 gap-y-6 sm:grid-cols-3">
                <div class="sm:col-span-1 ml-auto">
                    <dt class="text-sm font-medium text-gray-500">Subtotal</dt>
                    <dd class="mt-1 text-sm text-gray-900">₱{{ sale.subtotal|intcomma }}</dd>
                </div>
                <div class="sm:col-span-1 ml-auto">
                    <dt class="text-sm font-medium text-gray-500">Tax Amount</dt>
                    <dd class="mt-1 text-sm text-gray-900">₱{{ sale.tax_amount|intcomma }}</dd>
                </div>
                <div class="sm:col-span-1 ml-auto">
                    <dt class="text-sm font-medium text-gray-500">Total Amount</dt>
                    <dd class="mt-1 text-lg font-bold text-gray-900">₱{{ sale.total_amount|intcomma }}</dd>
                </div>
            </dl>
        </div>
    </div>
{% endblock %}
