<!DOCTYPE html>
<html lang="en">
    {% load static %}
    <head>
        <meta charset="UTF-8">
        <meta name="viewport" content="width=device-width, initial-scale=1.0">
        <title>POS System</title>
        <script src="https://cdn.tailwindcss.com"></script>
        <link href="https://fonts.googleapis.com/css2?family=Poppins:wght@300;400;500;600;700&display=swap" rel="stylesheet">
        <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.2/css/all.min.css">
        <style>
            body {
                font-family: 'Poppins', sans-serif;
                background: #f8fafc;
            }
            .card-hover {
                transition: all 0.3s ease;
            }
            .card-hover:hover {
                transform: translateY(-5px);
                box-shadow: 0 10px 20px rgba(0,0,0,0.1);
            }
            /* Add to existing styles */
            #productGrid > div {
                transition: all 0.3s ease-in-out;
            }

            .search-highlight {
                background-color: rgba(99, 102, 241, 0.1);
                transform: scale(1.05);
            }

            #productSearch, #barcodeInput {
                transition: all 0.2s ease;
            }

            #productSearch:focus, #barcodeInput:focus {
                box-shadow: 0 0 0 3px rgba(99, 102, 241, 0.2);
            }
            .hero-gradient {
                background: linear-gradient(135deg, #EBF4FF 0%, #C3DAFE 100%);
            }
        </style>
    </head>
    <body>
        <!-- Header -->
        <header class="bg-indigo-600 text-white py-6 transform hover:scale-[1.01] transition-all duration-300 shadow-lg">
            <div class="container mx-auto px-4">
                <div class="flex justify-between items-center">
                    <div class="flex items-center space-x-3">
                        <i class="fas fa-cash-register text-3xl text-indigo-200"></i>
                        <h1 class="text-3xl font-bold hover:text-indigo-200 transition-colors duration-300 animate-pulse">Smart<span class="text-yellow-300">POS</span></h1>
                    </div>
                    <div class="text-right transform hover:translate-y-[-2px] transition-transform duration-300 flex items-center space-x-3">
                        <i class="far fa-calendar-alt text-2xl text-indigo-200"></i>
                        <div>
                            <p class="text-sm opacity-90 hover:opacity-100 transition-opacity duration-300">{% now "F j, Y" %}</p>
                            <p class="text-xl font-semibold hover:text-indigo-200 transition-colors duration-300"><i class="far fa-clock mr-2"></i>{% now "H:i" %}</p>
                        </div>
                    </div>
                </div>
            </div>
        </header>

        <div class="container mx-auto px-4 py-6 bg-opacity-20 backdrop-filter backdrop-blur-lg bg-indigo-100/30 rounded-xl border border-indigo-200/50 shadow-lg">
            <div class="grid grid-cols-1 lg:grid-cols-3 gap-6">
                <!-- Main Content -->
                <div class="lg:col-span-2 space-y-6">
                    <!-- Barcode Scanner -->
                    <div class="bg-white rounded-lg shadow p-6 transform hover:scale-[1.02] transition-transform duration-300">
                        <div class="relative">
                            <i class="fas fa-barcode absolute left-4 top-4 text-gray-400 text-xl"></i>
                            <input type="text" id="barcodeInput"
                                   class="w-full pl-12 pr-4 py-3 text-lg border rounded-lg focus:ring-2 focus:ring-indigo-500 focus:border-indigo-500 transition-all duration-300"
                                   placeholder="Scan Barcode"
                                   autofocus>
                        </div>
                    </div>

                    <!-- Product Search -->
                    <div class="bg-white rounded-lg shadow p-6 transform hover:scale-[1.02] transition-transform duration-300">
                        <div class="relative mb-4">
                            <i class="fas fa-search absolute left-4 top-4 text-gray-400 text-xl"></i>
                            <input type="text" id="productSearch"
                                   class="w-full pl-12 pr-4 py-3 text-lg border rounded-lg focus:ring-2 focus:ring-indigo-500 focus:border-indigo-500 transition-all duration-300"
                                   placeholder="Search products...">
                        </div>

                        <!-- Advanced Filters -->
                        <div class="grid grid-cols-1 md:grid-cols-3 gap-4 mb-4">
                            <select id="categoryFilter" class="px-3 py-2 border rounded-lg focus:ring-2 focus:ring-indigo-500">
                                <option value="">All Categories</option>
                            </select>
                            <input type="number" id="minPrice" placeholder="Min Price"
                                   class="px-3 py-2 border rounded-lg focus:ring-2 focus:ring-indigo-500">
                            <input type="number" id="maxPrice" placeholder="Max Price"
                                   class="px-3 py-2 border rounded-lg focus:ring-2 focus:ring-indigo-500">
                        </div>

                        <div id="productGrid" class="grid grid-cols-2 md:grid-cols-3 gap-6 max-h-[60vh] overflow-y-auto p-4">
                            {% for product in products %}
                                <div class="card-hover bg-white rounded-xl p-6 border-2 border-gray-100"
                                     data-id="{{ product.id }}"
                                     data-barcode="{{ product.barcode }}"
                                     data-category="{{ product.category.name }}"
                                     data-price="{{ product.price }}">
                                    <div class="flex items-center justify-between mb-3">
                                        <div class="flex items-center space-x-2">
                                            <div class="bg-indigo-100 p-2 rounded-lg">
                                                <i class="fas fa-box text-indigo-600"></i>
                                            </div>
                                            <h3 class="font-bold text-gray-800 text-lg">{{ product.name|title }}</h3>
                                        </div>
                                    </div>
                                    <p class="text-indigo-600 font-bold text-2xl mt-3 flex items-center">
                                        <i class="fas fa-tag text-indigo-400 mr-2"></i>
                                        ₱{{ product.price }}
                                    </p>
                                    <div class="mt-4">
                                        <span class="inline-flex items-center px-4 py-2 text-sm font-medium rounded-full {% if product.stock_quantity <= 10 %}bg-red-100 text-red-800 border border-red-200{% else %}bg-green-100 text-green-800 border border-green-200{% endif %}">
                                            <i class="fas fa-cubes mr-2"></i>
                                            Stock: {{ product.stock_quantity }}
                                        </span>
                                    </div>
                                    <button onclick="addProduct('{{ product.id }}', '{{ product.name }}', '{{ product.price }}', '{{ product.stock_quantity }}')"
                                            class="w-full mt-4 bg-indigo-600 text-white py-2 rounded-lg font-semibold hover:bg-indigo-700 transition-colors flex items-center justify-center
                                                   {% if product.stock_quantity <= 0 %}opacity-50 cursor-not-allowed{% endif %}"
                                            {% if product.stock_quantity <= 0 %}disabled{% endif %}>
                                        <i class="fas fa-cart-plus mr-2"></i>
                                        {% if product.stock_quantity <= 0 %}
                                            Out of Stock
                                        {% else %}
                                            Add to Cart
                                        {% endif %}
                                    </button>
                                </div>
                            {% endfor %}
                        </div>

                        <!-- Load More Button -->
                        <div class="text-center mt-4">
                            <button id="loadMoreBtn" onclick="loadMoreProducts()"
                                    class="px-6 py-3 bg-indigo-600 text-white rounded-lg hover:bg-indigo-700 transition-colors hidden">
                                <i class="fas fa-plus mr-2"></i>
                                Load More Products
                            </button>
                        </div>
                    </div>
                </div>
                <!-- Cart -->
                <div class="lg:col-span-1">
                    <div class="bg-white rounded-lg shadow p-6 sticky top-6">
                        <div class="flex items-center justify-between mb-6">
                            <h2 class="text-xl font-bold flex items-center">
                                <i class="fas fa-shopping-cart text-indigo-600 mr-2"></i>
                                Shopping Cart
                            </h2>
                            <button onclick="resetCart()"
                                    class="px-3 py-1 bg-red-100 text-red-600 rounded-lg hover:bg-red-200 transition-colors flex items-center text-sm font-medium"
                                    title="Clear all items from cart">
                                <i class="fas fa-trash-alt mr-1"></i>
                                Reset
                            </button>
                        </div>
                        <div id="cartItems" class="space-y-4 max-h-[60vh] overflow-y-auto mb-6 border rounded-lg p-4 bg-gray-50">
                            <!-- Cart items here -->
                        </div>

                        <div class="border-t pt-4 space-y-3">
                            <div class="flex justify-between items-center">
                                <span class="flex items-center">
                                    <i class="fas fa-receipt text-gray-500 mr-2"></i>
                                    Subtotal
                                </span>
                                <span id="subtotal" class="font-medium">₱0.00</span>
                            </div>
                            <div class="flex justify-between items-center">
                                <span class="flex items-center">
                                    <i class="fas fa-percentage text-gray-500 mr-2"></i>
                                    Tax (12%)
                                </span>
                                <span id="tax" class="font-medium">₱0.00</span>
                            </div>
                            <div class="flex justify-between items-center text-xl font-bold text-indigo-600">
                                <span class="flex items-center">
                                    <i class="fas fa-coins mr-2"></i>
                                    Total
                                </span>
                                <span id="total">₱0.00</span>
                            </div>
                        </div>

                        <button onclick="showPaymentModal()"
                                class="w-full mt-6 bg-indigo-600 text-white py-3 rounded-lg font-semibold hover:bg-indigo-700 transition-colors flex items-center justify-center">
                            <i class="fas fa-credit-card mr-2"></i>
                            Proceed to Checkout
                        </button>
                    </div>
                </div>
            </div>
        </div>

        <!-- Cash Payment Modal -->
        <div id="paymentQRModal" class="hidden fixed inset-0 bg-black bg-opacity-50 z-50">
            <div class="relative top-10 mx-auto p-6 max-w-4xl bg-white rounded-lg shadow-xl">
                <h3 class="text-2xl font-bold mb-6 flex items-center">
                    <i class="fas fa-cash-register text-green-600 mr-3"></i>
                    Cash Payment
                </h3>

                <div class="grid grid-cols-1 lg:grid-cols-2 gap-8">
                    <!-- Order Summary -->
                    <div>
                        <div class="flex items-center mb-4">
                            <i class="fas fa-shopping-basket text-indigo-600 mr-2"></i>
                            <h4 class="font-semibold text-lg">Order Summary</h4>
                        </div>
                        <div id="orderItems" class="mb-6 max-h-64 overflow-y-auto bg-gray-50 p-4 rounded-lg border">
                            <!-- Order items here -->
                        </div>

                        <div class="border-t pt-4 space-y-3 bg-gray-50 p-4 rounded-lg">
                            <div class="flex justify-between items-center text-lg">
                                <span class="flex items-center">
                                    <i class="fas fa-receipt text-gray-500 mr-2"></i>
                                    Subtotal
                                </span>
                                <span id="modalSubtotal" class="font-medium">₱0.00</span>
                            </div>
                            <div class="flex justify-between items-center text-lg">
                                <span class="flex items-center">
                                    <i class="fas fa-percentage text-gray-500 mr-2"></i>
                                    Tax (12%)
                                </span>
                                <span id="modalTax" class="font-medium">₱0.00</span>
                            </div>
                            <div class="flex justify-between items-center text-2xl font-bold text-green-600 border-t pt-3">
                                <span class="flex items-center">
                                    <i class="fas fa-coins mr-2"></i>
                                    Total Amount
                                </span>
                                <span id="modalTotal">₱0.00</span>
                            </div>
                        </div>
                    </div>

                    <!-- Cash Calculator -->
                    <div>
                        <div class="space-y-6">
                            <!-- Cash Received Input -->
                            <div>
                                <label class="flex items-center text-lg font-semibold text-gray-700 mb-3">
                                    <i class="fas fa-money-bill-wave text-green-600 mr-2"></i>
                                    Cash Received
                                </label>
                                <input type="number" id="cashReceived" step="0.01" min="0"
                                       class="w-full px-4 py-4 text-2xl font-bold border-2 rounded-lg focus:ring-2 focus:ring-green-200 focus:border-green-600 transition-all text-center"
                                       placeholder="0.00" oninput="calculateChange()">
                            </div>

                            <!-- Quick Amount Buttons -->
                            <div>
                                <label class="block text-sm font-medium text-gray-700 mb-2">Quick Amount</label>
                                <div class="grid grid-cols-3 gap-2">
                                    <button onclick="setQuickAmount(100)" class="px-3 py-2 bg-gray-100 hover:bg-gray-200 rounded-lg font-medium">₱100</button>
                                    <button onclick="setQuickAmount(200)" class="px-3 py-2 bg-gray-100 hover:bg-gray-200 rounded-lg font-medium">₱200</button>
                                    <button onclick="setQuickAmount(500)" class="px-3 py-2 bg-gray-100 hover:bg-gray-200 rounded-lg font-medium">₱500</button>
                                    <button onclick="setQuickAmount(1000)" class="px-3 py-2 bg-gray-100 hover:bg-gray-200 rounded-lg font-medium">₱1,000</button>
                                    <button onclick="setExactAmount()" class="px-3 py-2 bg-blue-100 hover:bg-blue-200 rounded-lg font-medium text-blue-700">Exact</button>
                                    <button onclick="clearCashAmount()" class="px-3 py-2 bg-red-100 hover:bg-red-200 rounded-lg font-medium text-red-700">Clear</button>
                                </div>
                            </div>

                            <!-- Change Display -->
                            <div class="bg-yellow-50 border-2 border-yellow-200 rounded-lg p-4">
                                <div class="flex justify-between items-center">
                                    <span class="text-xl font-semibold text-yellow-800 flex items-center">
                                        <i class="fas fa-exchange-alt mr-2"></i>
                                        Change
                                    </span>
                                    <span id="changeAmount" class="text-3xl font-bold text-yellow-800">₱0.00</span>
                                </div>
                                <div id="changeStatus" class="text-sm text-yellow-700 mt-2"></div>
                            </div>

                            <!-- Action Buttons -->
                            <div class="flex gap-4 mt-8">
                                <button onclick="closePaymentModal()"
                                        class="flex-1 px-6 py-3 bg-gray-200 text-gray-700 rounded-lg hover:bg-gray-300 transition-colors flex items-center justify-center font-semibold">
                                    <i class="fas fa-times mr-2"></i>
                                    Cancel
                                </button>
                                <button id="saveAndPrintBtn" onclick="saveAndPrint()" disabled
                                        class="flex-1 px-6 py-3 bg-green-600 text-white rounded-lg hover:bg-green-700 transition-colors flex items-center justify-center font-semibold disabled:bg-gray-400 disabled:cursor-not-allowed">
                                    <i class="fas fa-save mr-2"></i>
                                    Save & Print
                                </button>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        {% block extra_js %}
            <script src="{% static 'js/sales.js' %}"></script>
        {% endblock %}
    </body>
</html>
<!-- Receipt Template -->
<div id="receiptTemplate" class="hidden">
    <div style="font-family: 'Courier New', monospace; width: 57mm; margin: 0 auto; padding: 5mm; background-color: #fff;">
        <div style="text-align: left; margin-bottom: 3mm; border-bottom: 1px solid #000; padding-bottom: 2mm;">
            <h2 style="font-size: 12pt; margin-bottom: 2mm; text-align: center;">REFERENCE RECEIPT</h2>
            <p style="font-size: 8pt; margin: 1mm 0;">Date: <span id="receiptDate"></span></p>
            <p style="font-size: 8pt; margin: 1mm 0;">Time: <span id="receiptTime"></span></p>
            <p style="font-size: 8pt; margin: 1mm 0;">Receipt #: <span id="receiptNumber"></span></p>
            <p style="font-size: 8pt; margin: 1mm 0;">Ref #: <span id="receiptReference"></span></p>
        </div>
        <div id="receiptItems" style="margin-bottom: 2mm; border-bottom: 1px dashed #000; padding-bottom: 2mm; font-size: 8pt; text-transform: uppercase;"></div>
        <div style="border-top: 1px solid #000; padding-top: 2mm; margin-top: 2mm;">
            <div style="display: flex; justify-content: space-between; margin: 1mm 0; font-size: 8pt;">
                <span>Subtotal:</span>
                <span id="receiptSubtotal"></span>
            </div>
            <div style="display: flex; justify-content: space-between; margin: 1mm 0; font-size: 8pt;">
                <span>Tax (12%):</span>
                <span id="receiptTax"></span>
            </div>
            <div style="display: flex; justify-content: space-between; font-weight: bold; margin-top: 2mm; padding-top: 2mm; border-top: 1px solid #000; font-size: 10pt;">
                <span>Total:</span>
                <span id="receiptTotal"></span>
            </div>
        </div>
        <div style="text-align: center; margin-top: 3mm; padding-top: 2mm; border-top: 1px solid #000;">
            <p style="font-size: 8pt;">Thank you for your purchase!</p>

            <!-- Reference Only Disclaimer -->
            <div style="margin-top: 3mm; padding-top: 2mm; border-top: 2px solid #000; font-size: 8pt; text-align: center; background-color: #f0f0f0;">
                <p style="margin: 1mm 0; font-weight: bold; font-size: 9pt;">*** FOR REFERENCE ONLY ***</p>
            </div>

            <!-- BIR Compliance Template (For Future Use) -->
            <div style="margin-top: 2mm; padding-top: 2mm; border-top: 1px dashed #000; font-size: 6pt; text-align: center; color: #888;">
                <p style="margin: 0.5mm 0;">When registered with BIR, update:</p>
                <p style="margin: 0.5mm 0;">TIN: [Your TIN Number]</p>
                <p style="margin: 0.5mm 0;">Permit No: [ATP Number]</p>
                <p style="margin: 0.5mm 0;">Date Issued: [ATP Date]</p>
            </div>
        </div>
    </div>
</div>