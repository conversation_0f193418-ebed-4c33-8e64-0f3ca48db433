
        // Select all functionality
        document.getElementById('selectAll').addEventListener('change', function() {
            document.querySelectorAll('.sale-checkbox').forEach(checkbox => {
                checkbox.checked = this.checked;
            });
        });

        // Individual delete
        function deleteSale(saleId) {
            if (confirm('Are you sure you want to delete this sale?')) {
                fetch(`/api/sales/${saleId}/delete/`, {
                    method: 'DELETE',
                    headers: {
                        'X-CSRFToken': getCookie('csrftoken')
                    }
                })
                .then(response => {
                    if (response.ok) {
                        window.location.reload();
                    }
                });
            }
        }

        // Bulk delete
        function bulkDelete() {
            const selectedSales = Array.from(document.querySelectorAll('.sale-checkbox:checked'))
                .map(checkbox => checkbox.value);
        
            if (selectedSales.length === 0) {
                alert('Please select sales to delete');
                return;
            }

            if (confirm(`Are you sure you want to delete ${selectedSales.length} sales?`)) {
                fetch('/api/sales/bulk-delete/', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                        'X-CSRFToken': getCookie('csrftoken')
                    },
                    body: JSON.stringify({sale_ids: selectedSales})
                })
                .then(response => {
                    if (response.ok) {
                        window.location.reload();
                    }
                });
            }
        }

        // CSRF token helper
        function getCookie(name) {
            let cookieValue = null;
            if (document.cookie && document.cookie !== '') {
                const cookies = document.cookie.split(';');
                for (let i = 0; i < cookies.length; i++) {
                    const cookie = cookies[i].trim();
                    if (cookie.substring(0, name.length + 1) === (name + '=')) {
                        cookieValue = decodeURIComponent(cookie.substring(name.length + 1));
                        break;
                    }
                }
            }
            return cookieValue;
        }
          function formatNumber(number) {
              return number.toString().replace(/\B(?=(\d{3})+(?!\d))/g, ",");
          }

          function reprintReceipt(saleId) {
              fetch(`/api/receipt/${saleId}/`)
                  .then(response => response.json())
                  .then(data => {
                      const receipt = document.createElement('div');
                      receipt.style.cssText = "font-family: 'Courier New', monospace; max-width: 300px; margin: 0 auto; padding: 20px; background-color: #fff; box-shadow: 0 0 10px rgba(0,0,0,0.1); border-radius: 8px;";

                      receipt.innerHTML = `
                          <div style="text-align: left; margin-bottom: 20px; border-bottom: 2px solid #333; padding-bottom: 15px;">
                              <h2 style="font-size: 24px; color: #2c3e50; margin-bottom: 15px; text-align: center;">SALES RECEIPT</h2>
                              <p style="color: #555; margin: 5px 0;">Date: <span style="font-weight: bold;">${data.date}</span></p>
                              <p style="color: #555; margin: 5px 0;">Receipt #: <span style="font-weight: bold;">${data.sale_id}</span></p>
                              <p style="color: #555; margin: 5px 0;">Reference #: <span style="font-weight: bold;">${data.payment_reference}</span></p>
                          </div>
                          <div style="margin-bottom: 20px; border-bottom: 1px dashed #ccc; padding-bottom: 15px; text-transform: uppercase;">
                              ${data.items.map(item => `
                                  <div style="display: flex; justify-content: space-between; margin: 8px 0;">
                                      <span>${item.name} x${item.quantity}</span>
                                      <span>₱${formatNumber(item.total.toFixed(2))}</span>
                                  </div>
                              `).join('')}
                          </div>
                          <div style="border-top: 2px solid #333; padding-top: 15px; margin-top: 15px;">
                              <div style="display: flex; justify-content: space-between; margin: 8px 0; color: #555;">
                                  <span>Subtotal:</span>
                                  <span style="font-weight: bold;">₱${formatNumber(data.subtotal.toFixed(2))}</span>
                              </div>
                              <div style="display: flex; justify-content: space-between; margin: 8px 0; color: #555;">
                                  <span>Tax (12%):</span>
                                  <span style="font-weight: bold;">₱${formatNumber(data.tax_amount.toFixed(2))}</span>
                              </div>
                              <div style="display: flex; justify-content: space-between; font-weight: bold; margin-top: 12px; padding-top: 12px; border-top: 1px solid #ccc; color: #2c3e50; font-size: 18px;">
                                  <span>Total:</span>
                                  <span>₱${formatNumber(data.total_amount.toFixed(2))}</span>
                              </div>
                          </div>
                          <div style="text-align: center; margin-top: 25px; padding-top: 15px; border-top: 2px solid #333;">
                              <p style="color: #2c3e50; font-size: 16px; font-style: italic;">Thank you for your purchase!</p>
                          </div>
                      `;

                      const printWindow = window.open('', 'Print Receipt', 'height=600,width=800');
                      printWindow.document.write(receipt.outerHTML);
                      printWindow.document.close();
                      printWindow.print();
                  });
          }
    // Add this at the top of the file
    document.addEventListener('DOMContentLoaded', function() {
        const searchForm = document.getElementById('searchForm');
        const searchInput = document.getElementById('searchInput');
    
        searchForm.addEventListener('submit', function(e) {
            e.preventDefault();
            const searchTerm = searchInput.value.trim();
            
            // Update URL with search parameter
            const currentUrl = new URL(window.location.href);
            currentUrl.searchParams.set('search', searchTerm);
            
            // Redirect to filtered results
            window.location.href = currentUrl.toString();
        });
    
        // Preserve search term in input after page reload
        const urlParams = new URLSearchParams(window.location.search);
        const searchTerm = urlParams.get('search');
        if (searchTerm) {
            searchInput.value = searchTerm;
        }
    });
    