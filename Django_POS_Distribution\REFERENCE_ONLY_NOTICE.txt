*** IMPORTANT NOTICE ***
========================

FOR REFERENCE ONLY - NOT FOR BUSINESS USE
==========================================

This Django POS System is currently NOT REGISTERED with the 
Bureau of Internal Revenue (BIR) and is intended for:

✓ DEMO PURPOSES
✓ TRAINING AND LEARNING
✓ DEVELOPMENT AND TESTING
✓ REFERENCE IMPLEMENTATION

❌ NOT VALID FOR:
- Official business transactions
- Tax reporting
- Legal receipts
- Commercial operations

RECEIPT DISCLAIMERS:
===================

All receipts generated by this system include:

*** FOR REFERENCE ONLY ***
THIS POS SYSTEM IS NOT REGISTERED WITH BIR
NOT VALID FOR OFFICIAL TRANSACTIONS
DEMO/TRAINING PURPOSES ONLY

LEGAL COMPLIANCE:
================

To use this system for actual business:

1. REGISTER YOUR BUSINESS with BIR
   - Obtain Certificate of Registration (COR)
   - Get your Tax Identification Number (TIN)

2. APPLY FOR AUTHORITY TO PRINT (ATP)
   - Submit ATP application to BIR
   - Specify receipt series range
   - Pay required fees

3. UPDATE THE POS SYSTEM
   - Configure your TIN number
   - Add ATP number and date
   - Update business information
   - Remove "reference only" disclaimers

4. REGISTER BOOKS OF ACCOUNTS
   - Register computerized books with BIR
   - Include this POS system in registration

PENALTIES FOR NON-COMPLIANCE:
============================

Using receipts without proper BIR registration can result in:
- ₱1,000 - ₱50,000 for using receipts without ATP
- ₱500 - ₱25,000 for incorrect TIN display
- ₱1,000 - ₱50,000 for missing compliance text
- Business closure in severe cases

CURRENT SYSTEM STATUS:
=====================

✓ Receipt templates include BIR compliance structure
✓ "Reference Only" disclaimers prominently displayed
✓ Ready for BIR configuration when registered
✓ Includes setup tools for future compliance

❌ No valid TIN number
❌ No ATP registration
❌ Not authorized for business use
❌ Receipts not legally valid

NEXT STEPS FOR BUSINESS USE:
===========================

1. Consult with BIR-accredited tax professional
2. Register your business with BIR
3. Obtain required permits and licenses
4. Configure this POS system with your BIR information
5. Test receipt printing before going live
6. Maintain proper books of accounts

SUPPORT:
=======

For BIR registration guidance:
- Visit your local BIR office
- Consult with Certified Public Accountant (CPA)
- Contact BIR-accredited tax professional

For technical POS system support:
- See USER_GUIDE.txt
- Check BIR_SETUP_INSTRUCTIONS.txt
- Review documentation in docs/ folder

DISCLAIMER:
==========

This software provides tools to help with BIR compliance but does not
guarantee legal compliance. Users are responsible for ensuring their
business operations comply with all applicable laws and regulations.

The developers of this POS system are not responsible for any legal
issues arising from improper use or non-compliance with BIR regulations.

Always consult with qualified tax professionals for legal advice.

*** USE AT YOUR OWN RISK ***
*** FOR REFERENCE ONLY ***
