{% extends 'base/base.html' %}
{% load humanize %}

{% block title %}Sales Report{% endblock %}

{% block content %}
    <div class="bg-white shadow rounded-lg p-6">
    <!-- Report Header -->
        <div class="mb-6">
            <h1 class="text-2xl font-bold text-gray-800"><i class="fas fa-chart-line mr-2"></i>Sales Report</h1>
        </div>

    <!-- Date Range Selector -->
        <div class="grid grid-cols-1 md:grid-cols-4 gap-4 mb-6">
            <button class="bg-blue-600 text-white px-4 py-2 rounded hover:bg-blue-700 transition-colors" data-range="daily">
                <i class="far fa-calendar mr-2"></i>Today's Report
            </button>
            <button class="bg-blue-600 text-white px-4 py-2 rounded hover:bg-blue-700 transition-colors" data-range="monthly">
                <i class="far fa-calendar-alt mr-2"></i>Monthly Report
            </button>
            <button class="bg-blue-600 text-white px-4 py-2 rounded hover:bg-blue-700 transition-colors" data-range="yearly">
                <i class="far fa-calendar mr-2"></i>Yearly Report
            </button>
            <button class="bg-gray-600 text-white px-4 py-2 rounded hover:bg-gray-700 transition-colors" id="customRange">
                <i class="fas fa-calendar-plus mr-2"></i>Custom Range
            </button>
        </div>

    <!-- Custom Date Range Form -->
        <div id="customRangeForm" class="hidden mb-6 p-4 rounded bg-gray-100">
            <form class="grid grid-cols-1 md:grid-cols-3 gap-4">
                <div>
                    <label class="block text-sm font-medium text-gray-700">Start Date</label>
                    <input type="date" name="start_date" class="mt-1 block w-full rounded-md border-gray-300 shadow-sm">
                </div>
                <div>
                    <label class="block text-sm font-medium text-gray-700">End Date</label>
                    <input type="date" name="end_date" class="mt-1 block w-full rounded-md border-gray-300 shadow-sm">
                </div>
                <div class="flex items-end">
                    <button type="submit" class="bg-blue-600 text-white px-4 py-2 rounded hover:bg-blue-700 transition-colors">
                        <i class="fas fa-search mr-2"></i>Generate Report
                    </button>
                </div>
            </form>
        </div>

    <!-- Summary Cards -->
        <div class="grid grid-cols-1 md:grid-cols-4 gap-4 mb-6">
            <div class="bg-gradient-to-r from-blue-500 to-blue-600 p-4 rounded-lg shadow-lg">
                <h3 class="text-lg font-semibold text-white"><i class="fas fa-money-bill-wave mr-2"></i>Total Sales</h3>
                <p class="text-2xl font-bold text-white">₱{{ total_sales|default:"0.00"|floatformat:2|intcomma }}</p>
            </div>
            <div class="bg-gradient-to-r from-green-500 to-green-600 p-4 rounded-lg shadow-lg">
                <h3 class="text-lg font-semibold text-white"><i class="fas fa-receipt mr-2"></i>Total Transactions</h3>
                <p class="text-2xl font-bold text-white">{{ transaction_count|default:"0"|intcomma }}</p>
            </div>
            <div class="bg-gradient-to-r from-purple-500 to-purple-600 p-4 rounded-lg shadow-lg">
                <h3 class="text-lg font-semibold text-white"><i class="fas fa-chart-bar mr-2"></i>Average Sale</h3>
                <p class="text-2xl font-bold average-sale text-white">₱{{ average_sale|default:"0.00"|floatformat:2|intcomma }}</p>
            </div>
            <div class="bg-gradient-to-r from-orange-500 to-orange-600 p-4 rounded-lg shadow-lg">
                <h3 class="text-lg font-semibold text-white"><i class="fas fa-percentage mr-2"></i>Total Tax</h3>
                <p class="text-2xl font-bold total-tax text-white">₱{{ total_tax|default:"0.00"|floatformat:2|intcomma }}</p>
            </div>
        </div>

    <!-- Sales Table -->
        <div class="overflow-x-auto bg-white rounded-lg shadow">
            <table class="min-w-full divide-y divide-gray-200" id="salesTable">
                <thead class="bg-gray-50">
                    <tr>
                        <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider"><i class="far fa-clock mr-1"></i>Date</th>
                        <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider"><i class="fas fa-hashtag mr-1"></i>Invoice #</th>
                        <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider"><i class="fas fa-calculator mr-1"></i>Subtotal</th>
                        <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider"><i class="fas fa-percent mr-1"></i>Tax</th>
                        <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider"><i class="fas fa-coins mr-1"></i>Total</th>
                        <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider"><i class="fas fa-check-circle mr-1"></i>Status</th>
                    </tr>
                </thead>
                <tbody class="bg-white divide-y divide-gray-200">
                    {% for sale in sales %}
                        <tr class="hover:bg-gray-50">
                            <td class="px-6 py-4 whitespace-nowrap text-gray-700">{{ sale.created_at|date:"Y-m-d H:i" }}</td>
                            <td class="px-6 py-4 whitespace-nowrap text-gray-700">#{{ sale.id }}</td>
                            <td class="px-6 py-4 whitespace-nowrap text-gray-700">₱{{ sale.subtotal|intcomma }}</td>
                            <td class="px-6 py-4 whitespace-nowrap text-gray-700">₱{{ sale.tax_amount|intcomma }}</td>
                            <td class="px-6 py-4 whitespace-nowrap text-gray-700">₱{{ sale.total_amount|intcomma }}</td>
                            <td class="px-6 py-4 whitespace-nowrap">
                                {% if sale.payment_status %}
                                    <span class="px-2 inline-flex text-xs leading-5 font-semibold rounded-full bg-green-100 text-green-800">
                                        <i class="fas fa-check-circle mr-1"></i>Paid
                                    </span>
                                {% else %}
                                    <span class="px-2 inline-flex text-xs leading-5 font-semibold rounded-full bg-yellow-100 text-yellow-800">
                                        <i class="fas fa-clock mr-1"></i>Pending
                                    </span>
                                {% endif %}
                            </td>
                        </tr>
                    {% endfor %}
                </tbody>
            </table>
        </div>
    </div>

{% endblock %}
{% block extra_js %}
    <script src="https://code.jquery.com/jquery-3.6.0.min.js"></script>
    <script src="https://cdn.datatables.net/1.11.5/js/jquery.dataTables.min.js"></script>
    <script>
        $(document).ready(function() {
            let dataTable = $('#salesTable').DataTable({
                order: [[0, 'desc']],
                pageLength: 25,
                responsive: true,
                dom: '<"flex flex-col md:flex-row justify-between items-center mb-4"lf>rtip'
            });

            function updateReport(data) {
                $('.total-sales').text('₱' + data.total_sales.toFixed(2));
                $('.transaction-count').text(data.transaction_count);
                $('.average-sale').text('₱' + data.average_sale.toFixed(2));
                $('.total-tax').text('₱' + data.total_tax.toFixed(2));

                dataTable.clear();
                data.sales.forEach(sale => {
                    dataTable.row.add([
                        new Date(sale.created_at).toLocaleString(),
                        '#' + sale.id,
                        '₱' + sale.subtotal,
                        '₱' + sale.tax_amount,
                        '₱' + sale.total_amount,
                        sale.payment_status ?
                        '<span class="px-2 inline-flex text-xs leading-5 font-semibold rounded-full bg-green-100 text-green-800"><i class="fas fa-check-circle mr-1"></i>Paid</span>' :
                            '<span class="px-2 inline-flex text-xs leading-5 font-semibold rounded-full bg-yellow-100 text-yellow-800"><i class="fas fa-clock mr-1"></i>Pending</span>'
                    ]);
                });
                dataTable.draw();
            }

            $('[data-range]').click(function() {
                const range = $(this).data('range');
                $.get('/sales/report/data/', { range: range }, updateReport);
            });

            $('#customRangeForm form').submit(function(e) {
                e.preventDefault();
                $.get('/sales/report/data/', {
                    range: 'custom',
                    start_date: $('input[name="start_date"]').val(),
                    end_date: $('input[name="end_date"]').val()
                }, updateReport);
            });

            $('#customRange').click(function() {
                $('#customRangeForm').toggleClass('hidden');
            });
        });
    </script>
{% endblock %}

{% block extra_css %}
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
    <style>
        .dataTables_wrapper {
            padding: 1rem;
            background-color: white;
            border-radius: 0.5rem;
            box-shadow: 0 1px 3px 0 rgba(0, 0, 0, 0.1);
        }

        .dataTables_filter input {
            border: 1px solid #e2e8f0;
            border-radius: 0.375rem;
            padding: 0.5rem;
            margin-left: 0.5rem;
        }

        .dataTables_length select {
            border: 1px solid #e2e8f0;
            border-radius: 0.375rem;
            padding: 0.5rem;
            margin: 0 0.5rem;
        }

        .dataTables_paginate .paginate_button {
            padding: 0.5rem 1rem;
            margin: 0 0.25rem;
            border-radius: 0.375rem;
            background-color: #f3f4f6;
            transition: all 0.2s;
        }

        .dataTables_paginate .paginate_button:hover {
            background-color: #e5e7eb;
        }

        .dataTables_paginate .paginate_button.current {
            background-color: #3b82f6;
            color: white !important;
        }

        .dataTables_paginate .paginate_button.current:hover {
            background-color: #2563eb;
        }
    </style>
{% endblock %}
