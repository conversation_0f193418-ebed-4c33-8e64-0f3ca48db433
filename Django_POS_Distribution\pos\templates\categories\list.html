{% extends 'base/base.html' %}

{% block title %}Categories - POS System{% endblock %}

{% block content %}
    <div class="bg-white shadow overflow-hidden sm:rounded-lg">
        <div class="px-4 py-5 sm:px-6 flex justify-between items-center">
            <h1 class="text-2xl font-semibold text-gray-900 flex items-center">
                <i class="fas fa-folder-open text-indigo-600 mr-3"></i>
                Categories
                <span class="ml-3 text-sm text-gray-500">({{ page_obj.paginator.count|default:categories.count }} total)</span>
            </h1>
            <div class="flex space-x-3">
                {% if categories %}
                    <button onclick="confirmDeleteAll()" class="bg-red-600 text-white px-4 py-2 rounded-md hover:bg-red-700 transition-colors duration-200 flex items-center">
                        <i class="fas fa-trash-alt mr-2"></i>Delete All
                    </button>
                {% endif %}
                <button onclick="openModal()" class="bg-indigo-600 text-white px-4 py-2 rounded-md hover:bg-indigo-700 transition-colors duration-200 flex items-center">
                    <i class="fas fa-plus mr-2"></i>Add Category
                </button>
            </div>
        </div>

        <!-- Live Search Section -->
        <div class="px-4 py-4 border-b border-gray-200">
            <div class="flex items-center space-x-4">
                <div class="flex-1">
                    <div class="relative">
                        <input type="text"
                               id="categorySearchInput"
                               placeholder="Search categories..."
                               value="{{ search_query }}"
                               class="w-full pl-10 pr-4 py-2 border-2 border-gray-200 rounded-lg focus:outline-none focus:ring-2 focus:ring-indigo-500 focus:border-indigo-500 transition-all duration-200">
                        <div class="absolute left-3 top-2.5 text-gray-400">
                            <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5" viewBox="0 0 20 20" fill="currentColor">
                                <path fill-rule="evenodd" d="M8 4a4 4 0 100 8 4 4 0 000-8zM2 8a6 6 0 1110.89 3.476l4.817 4.817a1 1 0 01-1.414 1.414l-4.816-4.816A6 6 0 012 8z" clip-rule="evenodd" />
                            </svg>
                        </div>
                        <div id="searchSpinner" class="absolute right-3 top-2.5 text-gray-400 hidden">
                            <svg class="animate-spin h-5 w-5" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                                <circle class="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" stroke-width="4"></circle>
                                <path class="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                            </svg>
                        </div>
                    </div>
                </div>
                {% if search_query %}
                    <a href="{% url 'categories' %}" class="px-4 py-2 bg-gray-500 text-white rounded-lg hover:bg-gray-600 transition-colors duration-200">
                        <i class="fas fa-times mr-2"></i>Clear
                    </a>
                {% endif %}
            </div>

            <!-- Live Search Results -->
            <div id="liveSearchResults" class="mt-4 hidden">
                <div class="bg-blue-50 border border-blue-200 rounded-lg p-4">
                    <h3 class="text-sm font-medium text-blue-800 mb-2">Live Search Results</h3>
                    <div id="liveSearchContent" class="space-y-2"></div>
                </div>
            </div>
        </div>

        <div class="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-6 p-6">
            {% for category in categories %}
                <div class="bg-white overflow-hidden shadow-sm rounded-lg border border-gray-200">
                    <div class="p-6">
                        <div class="flex items-center justify-between mb-4">
                            <h3 class="text-lg font-medium text-gray-900">{{ category.name }}</h3>
                            <span class="px-2 py-1 text-xs font-semibold rounded-full bg-indigo-100 text-indigo-800">
                                <i class="fas fa-box mr-1"></i>{{ category.products.count }} Products
                            </span>
                        </div>
                        <div class="text-sm text-gray-600 mb-4">
                            <i class="far fa-calendar-alt mr-2"></i>Created: {{ category.created_at|date:"M d, Y" }}
                        </div>
                        <div class="flex justify-end space-x-3">
                            <button onclick="editCategory('{{ category.id }}', '{{ category.name }}')"
                                    class="text-indigo-600 hover:text-indigo-900"><i class="fas fa-edit mr-1"></i>Edit</button>
                            <button onclick="deleteCategory('{{ category.id }}')"
                                    class="text-red-600 hover:text-red-900"><i class="fas fa-trash-alt mr-1"></i>Delete</button>
                        </div>
                    </div>
                </div>
            {% endfor %}
        </div>
    </div>

<!-- Add Category Modal -->
    <div id="categoryModal" class="hidden fixed inset-0 bg-gray-500 bg-opacity-75 overflow-y-auto h-full w-full">
        <div class="relative top-20 mx-auto p-5 border w-96 shadow-lg rounded-md bg-white">
            <div class="mt-3">
                <h3 id="modalTitle" class="text-lg font-medium leading-6 text-gray-900 mb-4"><i class="fas fa-folder-plus mr-2"></i>Add New Category</h3>
                <form id="categoryForm" method="POST" action="{% url 'add_category' %}">
                    {% csrf_token %}
                    <input type="hidden" id="categoryId" name="category_id">

                    <div class="mb-4">
                        <label class="block text-gray-700 text-sm font-bold mb-2" for="name">
                            <i class="fas fa-tag mr-2"></i>Category Name
                        </label>
                        <input type="text" name="name" id="categoryName" required
                               class="shadow appearance-none border rounded w-full py-2 px-3 text-gray-700 leading-tight focus:outline-none focus:shadow-outline">
                    </div>

                    <div class="flex items-center justify-between mt-6">
                        <button type="button" onclick="closeModal()"
                                class="bg-gray-500 text-white px-4 py-2 rounded-md hover:bg-gray-600">
                            <i class="fas fa-times mr-2"></i>Cancel
                        </button>
                        <button type="submit"
                                class="bg-indigo-600 text-white px-4 py-2 rounded-md hover:bg-indigo-700">
                            <i class="fas fa-save mr-2"></i>Save Category
                        </button>
                    </div>
                </form>
            </div>
        </div>
    </div>
{% endblock %}

{% block extra_js %}
    <script>
        function openModal() {
            document.getElementById('modalTitle').innerHTML = '<i class="fas fa-folder-plus mr-2"></i>Add New Category';
            document.getElementById('categoryForm').action = "{% url 'add_category' %}";
            document.getElementById('categoryId').value = '';
            document.getElementById('categoryName').value = '';
            document.getElementById('categoryModal').classList.remove('hidden');
        }

        function closeModal() {
            document.getElementById('categoryModal').classList.add('hidden');
        }

        function editCategory(id, name) {
            document.getElementById('modalTitle').innerHTML = '<i class="fas fa-edit mr-2"></i>Edit Category';
            document.getElementById('categoryForm').action = "{% url 'edit_category' %}";
            document.getElementById('categoryId').value = id;
            document.getElementById('categoryName').value = name;
            document.getElementById('categoryModal').classList.remove('hidden');
        }

        function deleteCategory(id) {
            if (confirm('Are you sure you want to delete this category?')) {
                window.location.href = `/categories/delete/${id}/`;
            }
        }

        function confirmDeleteAll() {
            const categoryCount = {{ categories.count }};
            if (categoryCount === 0) {
                alert('No categories to delete.');
                return;
            }

            const confirmMessage = `⚠️ WARNING: This will permanently delete ALL ${categoryCount} categories!\n\n` +
                                 `This action cannot be undone and will also affect any products assigned to these categories.\n\n` +
                                 `Are you absolutely sure you want to continue?`;

            if (confirm(confirmMessage)) {
                // Show loading state
                const deleteBtn = event.target;
                const originalText = deleteBtn.innerHTML;
                deleteBtn.innerHTML = '<i class="fas fa-spinner fa-spin mr-2"></i>Deleting...';
                deleteBtn.disabled = true;

                // Create and submit form
                const form = document.createElement('form');
                form.method = 'POST';
                form.action = '{% url "delete_all_categories" %}';

                const csrfToken = document.createElement('input');
                csrfToken.type = 'hidden';
                csrfToken.name = 'csrfmiddlewaretoken';
                csrfToken.value = '{{ csrf_token }}';
                form.appendChild(csrfToken);

                document.body.appendChild(form);
                form.submit();
            }
        }

    // Close modal when clicking outside
        window.onclick = function(event) {
            const modal = document.getElementById('categoryModal');
            if (event.target == modal) {
                closeModal();
            }
        }
    </script>
{% endblock %}