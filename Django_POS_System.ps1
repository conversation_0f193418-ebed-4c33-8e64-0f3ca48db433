# Django POS System Desktop Launcher (PowerShell)
# Right-click and "Run with PowerShell" or double-click if execution policy allows

param(
    [switch]$Silent
)

# Set window title
$Host.UI.RawUI.WindowTitle = "Django POS System"

# Change to script directory
Set-Location $PSScriptRoot

if (-not $Silent) {
    Write-Host ""
    Write-Host "========================================" -ForegroundColor Cyan
    Write-Host "    Django POS System Starting..." -ForegroundColor Cyan
    Write-Host "========================================" -ForegroundColor Cyan
    Write-Host ""
}

# Check if Python is available
try {
    $pythonVersion = python --version 2>&1
    if ($LASTEXITCODE -ne 0) {
        throw "Python not found"
    }
    if (-not $Silent) {
        Write-Host "✅ Python found: $pythonVersion" -ForegroundColor Green
    }
} catch {
    Write-Host "❌ ERROR: Python is not installed or not in PATH" -ForegroundColor Red
    Write-Host "Please install Python and try again." -ForegroundColor Yellow
    if (-not $Silent) {
        Read-Host "Press Enter to exit"
    }
    exit 1
}

# Check if dependencies are installed
try {
    python -c "import django, pystray" 2>$null
    if ($LASTEXITCODE -ne 0) {
        if (-not $Silent) {
            Write-Host "⚠️  Installing dependencies..." -ForegroundColor Yellow
        }
        uv pip install -r requirements_desktop.txt
        if ($LASTEXITCODE -ne 0) {
            throw "Failed to install dependencies"
        }
    }
    if (-not $Silent) {
        Write-Host "✅ Dependencies OK" -ForegroundColor Green
    }
} catch {
    Write-Host "❌ ERROR: Failed to install dependencies" -ForegroundColor Red
    if (-not $Silent) {
        Read-Host "Press Enter to exit"
    }
    exit 1
}

# Start the desktop application
if (-not $Silent) {
    Write-Host ""
    Write-Host "🚀 Starting Django POS System..." -ForegroundColor Green
    Write-Host "The system will open in your browser automatically." -ForegroundColor Cyan
    Write-Host ""
}

try {
    python desktop_launcher.py
} catch {
    Write-Host "❌ ERROR: Failed to start POS system" -ForegroundColor Red
    Write-Host $_.Exception.Message -ForegroundColor Red
}

if (-not $Silent) {
    Write-Host ""
    Write-Host "POS system has stopped." -ForegroundColor Yellow
    Read-Host "Press Enter to exit"
}
