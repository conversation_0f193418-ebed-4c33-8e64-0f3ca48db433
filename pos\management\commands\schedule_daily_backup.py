"""
Django management command to schedule daily backups
Usage: python manage.py schedule_daily_backup
"""

import os
import sys
import platform
from pathlib import Path
from django.core.management.base import BaseCommand
from django.conf import settings

class Command(BaseCommand):
    help = 'Schedule daily automatic backups'

    def add_arguments(self, parser):
        parser.add_argument(
            '--time',
            type=str,
            default='02:00',
            help='Time to run backup (HH:MM format, default: 02:00)',
        )
        parser.add_argument(
            '--disable',
            action='store_true',
            help='Disable scheduled backup',
        )

    def handle(self, *args, **options):
        self.backup_time = options['time']
        self.disable = options['disable']
        
        self.stdout.write(self.style.SUCCESS('Daily Backup Scheduler'))
        self.stdout.write('=' * 30)
        
        if self.disable:
            self.disable_scheduled_backup()
        else:
            self.setup_scheduled_backup()

    def setup_scheduled_backup(self):
        """Setup scheduled backup based on operating system"""
        system = platform.system().lower()
        
        if system == 'windows':
            self.setup_windows_task()
        elif system in ['linux', 'darwin']:  # Linux or macOS
            self.setup_cron_job()
        else:
            self.stdout.write(
                self.style.ERROR(f'Unsupported operating system: {system}')
            )

    def setup_windows_task(self):
        """Setup Windows Task Scheduler"""
        self.stdout.write('Setting up Windows Task Scheduler...')
        
        # Get project paths
        project_dir = Path(settings.BASE_DIR)
        python_exe = sys.executable
        manage_py = project_dir / 'manage.py'
        
        # Create batch file for backup
        batch_file = project_dir / 'daily_backup.bat'
        batch_content = f'''@echo off
cd /d "{project_dir}"
"{python_exe}" "{manage_py}" backup_database --compress --keep-days 30
'''
        
        try:
            with open(batch_file, 'w') as f:
                f.write(batch_content)
            
            self.stdout.write(f'✅ Created backup script: {batch_file}')
            
            # Create task scheduler command
            task_name = 'POS_Daily_Backup'
            schtasks_cmd = f'''schtasks /create /tn "{task_name}" /tr "{batch_file}" /sc daily /st {self.backup_time} /f'''
            
            self.stdout.write()
            self.stdout.write('To complete setup, run this command as Administrator:')
            self.stdout.write('-' * 50)
            self.stdout.write(schtasks_cmd)
            self.stdout.write('-' * 50)
            self.stdout.write()
            self.stdout.write('Or manually create a task in Task Scheduler:')
            self.stdout.write(f'  - Task Name: {task_name}')
            self.stdout.write(f'  - Program: {batch_file}')
            self.stdout.write(f'  - Schedule: Daily at {self.backup_time}')
            
        except Exception as e:
            self.stdout.write(
                self.style.ERROR(f'Error creating Windows task: {e}')
            )

    def setup_cron_job(self):
        """Setup Linux/macOS cron job"""
        self.stdout.write('Setting up cron job...')
        
        # Get project paths
        project_dir = Path(settings.BASE_DIR)
        python_exe = sys.executable
        manage_py = project_dir / 'manage.py'
        
        # Convert time to cron format
        hour, minute = self.backup_time.split(':')
        
        # Create cron entry
        cron_entry = f'{minute} {hour} * * * cd "{project_dir}" && "{python_exe}" "{manage_py}" backup_database --compress --keep-days 30'
        
        # Create shell script for backup
        script_file = project_dir / 'daily_backup.sh'
        script_content = f'''#!/bin/bash
cd "{project_dir}"
"{python_exe}" "{manage_py}" backup_database --compress --keep-days 30
'''
        
        try:
            with open(script_file, 'w') as f:
                f.write(script_content)
            
            # Make script executable
            os.chmod(script_file, 0o755)
            
            self.stdout.write(f'✅ Created backup script: {script_file}')
            self.stdout.write()
            self.stdout.write('To complete setup, add this to your crontab:')
            self.stdout.write('-' * 50)
            self.stdout.write(cron_entry)
            self.stdout.write('-' * 50)
            self.stdout.write()
            self.stdout.write('Run: crontab -e')
            self.stdout.write('Then add the line above to schedule daily backups')
            
        except Exception as e:
            self.stdout.write(
                self.style.ERROR(f'Error creating cron job: {e}')
            )

    def disable_scheduled_backup(self):
        """Disable scheduled backup"""
        system = platform.system().lower()
        
        if system == 'windows':
            self.stdout.write('To disable Windows scheduled backup:')
            self.stdout.write('schtasks /delete /tn "POS_Daily_Backup" /f')
        else:
            self.stdout.write('To disable cron backup:')
            self.stdout.write('1. Run: crontab -e')
            self.stdout.write('2. Remove the POS backup line')
            self.stdout.write('3. Save and exit')
        
        # Remove backup scripts
        project_dir = Path(settings.BASE_DIR)
        
        for script_file in ['daily_backup.bat', 'daily_backup.sh']:
            script_path = project_dir / script_file
            if script_path.exists():
                try:
                    script_path.unlink()
                    self.stdout.write(f'✅ Removed: {script_file}')
                except Exception as e:
                    self.stdout.write(
                        self.style.ERROR(f'Error removing {script_file}: {e}')
                    )

    def show_backup_info(self):
        """Show backup information"""
        self.stdout.write()
        self.stdout.write('Backup Information:')
        self.stdout.write('-' * 20)
        self.stdout.write(f'Schedule: Daily at {self.backup_time}')
        self.stdout.write('Retention: 30 days')
        self.stdout.write('Compression: Enabled')
        self.stdout.write('Location: backups/ directory')
        self.stdout.write()
        self.stdout.write('What gets backed up:')
        self.stdout.write('- All database data (JSON format)')
        self.stdout.write('- SQLite database file')
        self.stdout.write('- Media files (product images)')
        self.stdout.write('- Static files (if collected)')
        self.stdout.write()
        self.stdout.write('Manual backup: python manage.py backup_database')
        self.stdout.write('View backups: Visit /backup/ in your POS system')
