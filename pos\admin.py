
from django.contrib import admin
from .models import Category, Product, PaymentQRCode, Sale, SaleItem

@admin.register(Category)
class CategoryAdmin(admin.ModelAdmin):
    list_display = ('name', 'created_at')
    search_fields = ('name',)

@admin.register(Product)
class ProductAdmin(admin.ModelAdmin):
    list_display = ('name', 'barcode', 'category', 'price', 'stock_quantity', 'price_with_tax')
    list_filter = ('category', 'created_at')
    search_fields = ('name', 'barcode', 'description')
    readonly_fields = ('created_at', 'updated_at')

@admin.register(PaymentQRCode)
class PaymentQRCodeAdmin(admin.ModelAdmin):
    list_display = ('name', 'is_active', 'created_at')
    list_filter = ('is_active',)
    search_fields = ('name',)

class SaleItemInline(admin.TabularInline):
    model = SaleItem
    extra = 1
    readonly_fields = ('tax_amount', 'subtotal', 'total_with_tax')

@admin.register(Sale)
class SaleAdmin(admin.ModelAdmin):
    list_display = ('id', 'cashier', 'subtotal', 'tax_amount', 'total_amount', 'payment_status', 'created_at')
    list_filter = ('payment_status', 'created_at', 'cashier')
    search_fields = ('payment_reference',)
    readonly_fields = ('subtotal', 'tax_amount', 'total_amount')
    inlines = [SaleItemInline]

@admin.register(SaleItem)
class SaleItemAdmin(admin.ModelAdmin):
    list_display = ('sale', 'product', 'quantity', 'price_at_sale', 'tax_amount', 'subtotal', 'total_with_tax')
    list_filter = ('sale__created_at',)
    search_fields = ('product__name', 'sale__payment_reference')
    readonly_fields = ('price_at_sale', 'tax_amount', 'subtotal', 'total_with_tax')
