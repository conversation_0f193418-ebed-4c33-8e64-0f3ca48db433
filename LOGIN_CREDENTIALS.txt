Django POS System - Login Credentials
====================================

DEFAULT USER ACCOUNTS:
======================

Regular User Account:
--------------------
Username: owner
Password: owner123
Email: <EMAIL>
Access: Standard POS operations

Administrator Account:
---------------------
Username: admin
Password: admin123
Email: <EMAIL>
Access: Full system administration

FIRST TIME LOGIN:
================
1. Start the POS system (double-click desktop shortcut)
2. <PERSON><PERSON><PERSON> opens automatically
3. Use either account above to login
4. Start using the POS system!

NOTES:
======
- These accounts are created automatically during setup
- You can change passwords after first login
- Admin account has full access to Django admin panel
- Owner account is for daily POS operations
- Both accounts can access all POS features

SECURITY:
=========
- Change default passwords after first login
- Create additional user accounts as needed
- Admin account can manage all users and settings
