#!/usr/bin/env python3
"""
System Tray Integration for Django POS Desktop Application
Provides system tray icon with menu options to control the POS system.
"""

import os
import sys
import time
import threading
import webbrowser
import subprocess
from pathlib import Path
import logging

try:
    import pystray
    from PIL import Image, ImageDraw
except ImportError:
    print("Required packages not installed. Please run: pip install pystray Pillow")
    sys.exit(1)

# Add project directory to path
PROJECT_DIR = Path(__file__).resolve().parent
sys.path.insert(0, str(PROJECT_DIR))

from desktop_launcher import DjangoPOSDesktop

logger = logging.getLogger(__name__)

class POSSystemTray:
    def __init__(self):
        self.pos_app = DjangoPOSDesktop()
        self.icon = None
        self.server_running = False
        
    def create_icon_image(self):
        """Create a simple icon for the system tray"""
        # Create a simple icon with "POS" text
        width = 64
        height = 64
        image = Image.new('RGB', (width, height), color='blue')
        draw = ImageDraw.Draw(image)
        
        # Draw "POS" text
        try:
            # Try to use a better font if available
            from PIL import ImageFont
            font = ImageFont.load_default()
        except:
            font = None
            
        text = "POS"
        if font:
            bbox = draw.textbbox((0, 0), text, font=font)
            text_width = bbox[2] - bbox[0]
            text_height = bbox[3] - bbox[1]
        else:
            text_width = len(text) * 8
            text_height = 12
            
        x = (width - text_width) // 2
        y = (height - text_height) // 2
        
        draw.text((x, y), text, fill='white', font=font)
        
        return image
    
    def start_pos_system(self, icon=None, item=None):
        """Start the POS system"""
        if self.server_running:
            self.show_notification("POS System", "Already running!")
            return
            
        def start_server():
            try:
                logger.info("Starting POS system from system tray...")
                
                # Setup and start the server
                if not self.pos_app.check_dependencies():
                    self.show_notification("POS System", "Dependencies missing!")
                    return
                
                if not self.pos_app.setup_django_environment():
                    self.show_notification("POS System", "Failed to setup Django!")
                    return
                
                if not self.pos_app.run_migrations():
                    self.show_notification("POS System", "Migration failed!")
                    return
                
                self.pos_app.collect_static_files()
                
                if self.pos_app.start_django_server():
                    self.server_running = True
                    self.show_notification("POS System", f"Started at {self.pos_app.url}")
                    self.update_menu()
                else:
                    self.show_notification("POS System", "Failed to start server!")
                    
            except Exception as e:
                logger.error(f"Failed to start POS system: {e}")
                self.show_notification("POS System", f"Error: {str(e)}")
        
        # Start in a separate thread to avoid blocking the UI
        threading.Thread(target=start_server, daemon=True).start()
    
    def stop_pos_system(self, icon=None, item=None):
        """Stop the POS system"""
        if not self.server_running:
            self.show_notification("POS System", "Not running!")
            return
            
        try:
            self.pos_app.cleanup()
            self.server_running = False
            self.show_notification("POS System", "Stopped successfully")
            self.update_menu()
        except Exception as e:
            logger.error(f"Failed to stop POS system: {e}")
            self.show_notification("POS System", f"Error stopping: {str(e)}")
    
    def open_pos_browser(self, icon=None, item=None):
        """Open POS system in browser"""
        if not self.server_running:
            self.show_notification("POS System", "Please start the system first!")
            return
            
        try:
            webbrowser.open(self.pos_app.url)
        except Exception as e:
            logger.error(f"Failed to open browser: {e}")
            self.show_notification("POS System", "Failed to open browser!")
    
    def restart_pos_system(self, icon=None, item=None):
        """Restart the POS system"""
        self.stop_pos_system()
        time.sleep(2)  # Wait a moment for cleanup
        self.start_pos_system()
    
    def show_status(self, icon=None, item=None):
        """Show current status"""
        status = "Running" if self.server_running else "Stopped"
        url = self.pos_app.url if self.server_running else "N/A"
        message = f"Status: {status}\nURL: {url}"
        self.show_notification("POS System Status", message)
    
    def open_logs(self, icon=None, item=None):
        """Open logs directory"""
        try:
            logs_dir = PROJECT_DIR / 'logs'
            if logs_dir.exists():
                os.startfile(str(logs_dir))
            else:
                self.show_notification("POS System", "No logs directory found!")
        except Exception as e:
            logger.error(f"Failed to open logs: {e}")
    
    def show_notification(self, title, message):
        """Show system notification"""
        if self.icon:
            self.icon.notify(message, title)
    
    def update_menu(self):
        """Update the system tray menu based on current state"""
        if not self.icon:
            return
            
        menu_items = []
        
        if self.server_running:
            menu_items.extend([
                pystray.MenuItem("Open POS", self.open_pos_browser, default=True),
                pystray.MenuItem("Stop POS", self.stop_pos_system),
                pystray.MenuItem("Restart POS", self.restart_pos_system),
                pystray.Menu.SEPARATOR,
            ])
        else:
            menu_items.extend([
                pystray.MenuItem("Start POS", self.start_pos_system, default=True),
                pystray.Menu.SEPARATOR,
            ])
        
        menu_items.extend([
            pystray.MenuItem("Status", self.show_status),
            pystray.MenuItem("Open Logs", self.open_logs),
            pystray.Menu.SEPARATOR,
            pystray.MenuItem("Exit", self.quit_application),
        ])
        
        self.icon.menu = pystray.Menu(*menu_items)
    
    def quit_application(self, icon=None, item=None):
        """Quit the application"""
        if self.server_running:
            self.stop_pos_system()
        
        if self.icon:
            self.icon.stop()
    
    def run(self):
        """Run the system tray application"""
        try:
            # Create the icon
            image = self.create_icon_image()
            self.icon = pystray.Icon(
                "pos_system",
                image,
                "Django POS System",
                menu=pystray.Menu()
            )
            
            # Update menu
            self.update_menu()
            
            # Show startup notification
            self.show_notification("POS System", "System tray started. Right-click to access menu.")
            
            # Run the icon (this blocks)
            self.icon.run()
            
        except Exception as e:
            logger.error(f"System tray error: {e}")
            print(f"Failed to start system tray: {e}")

def main():
    """Main entry point for system tray application"""
    # Setup logging
    logging.basicConfig(
        level=logging.INFO,
        format='%(asctime)s - %(levelname)s - %(message)s'
    )
    
    # Create and run system tray
    tray_app = POSSystemTray()
    tray_app.run()

if __name__ == '__main__':
    main()
